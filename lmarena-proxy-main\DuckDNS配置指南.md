# DuckDNS 配置指南

## 什么是 DuckDNS？

DuckDNS 是一个免费的动态DNS服务，可以为您的动态IP地址提供一个固定的域名。这样即使您的公网IP地址发生变化，也可以通过固定的域名访问您的服务。

## 配置步骤

### 1. 注册 DuckDNS 账户

1. 访问 [DuckDNS官网](https://www.duckdns.org)
2. 使用GitHub、Google、Twitter或Reddit账户登录
3. 登录后您会看到您的账户信息和token

### 2. 创建域名

1. 在DuckDNS控制面板中，找到"domains"部分
2. 在输入框中输入您想要的子域名（如：`ll555`）
3. 点击"add domain"按钮
4. 您的完整域名将是：`ll555.duckdns.org`

### 3. 获取Token

在DuckDNS控制面板顶部，您会看到您的token，类似于：
```
2ac70818-cacc-48a1-9dc1-7360875ccc72
```

### 4. 在代理服务器中配置

1. 启动您的代理服务器
2. 访问监控面板：`http://localhost:9080/monitor`
3. 在"设置"标签页中找到"内网穿透配置"
4. 勾选"启用内网穿透"
5. 选择隧道类型为"DuckDNS (免费动态域名)"
6. 填入配置信息：
   - **DuckDNS域名**：`ll555.duckdns.org`（您注册的完整域名）
   - **DuckDNS Token**：`2ac70818-cacc-48a1-9dc1-7360875ccc72`（您的token）
7. 点击"保存配置"
8. 点击"启动隧道"

### 5. 验证配置

配置成功后，您应该能够：

1. 通过 `http://ll555.duckdns.org:9080` 访问您的代理服务器
2. 在监控面板中看到"隧道状态：运行中"
3. 看到公网访问地址显示为您的DuckDNS域名

## 工作原理

1. **IP检测**：代理服务器会自动检测您的当前公网IP地址
2. **DNS更新**：每5分钟自动更新DuckDNS记录，将您的域名指向当前IP
3. **访问代理**：外部用户可以通过您的DuckDNS域名访问您的代理服务器

## 注意事项

### 端口访问
- DuckDNS只提供域名解析，不提供端口转发
- 您需要确保路由器已经设置了端口转发，将外部的9080端口转发到您的电脑
- 或者您可以修改代理服务器端口为80（需要管理员权限）

### 路由器配置
如果您在家庭网络中，需要在路由器中设置端口转发：
1. 登录路由器管理界面
2. 找到"端口转发"或"虚拟服务器"设置
3. 添加规则：外部端口9080 → 内部IP（您的电脑IP）端口9080

### 防火墙设置
确保您的防火墙允许9080端口的入站连接。

## 故障排除

### 1. 无法访问域名
- 检查DuckDNS域名是否正确配置
- 确认token是否正确
- 检查路由器端口转发设置
- 确认防火墙设置

### 2. IP更新失败
- 检查网络连接
- 确认DuckDNS token有效
- 查看代理服务器日志中的错误信息

### 3. 域名解析问题
- 等待DNS传播（可能需要几分钟）
- 尝试使用不同的DNS服务器
- 使用 `nslookup ll555.duckdns.org` 检查域名解析

## 优势

- ✅ **完全免费**：无需付费，无使用限制
- ✅ **自动更新**：IP变化时自动更新DNS记录
- ✅ **稳定可靠**：DuckDNS服务稳定，很少宕机
- ✅ **简单易用**：配置简单，维护方便
- ✅ **支持SSL**：可以为DuckDNS域名申请免费SSL证书

## 与其他方案对比

| 方案 | 费用 | 配置难度 | 稳定性 | 推荐指数 |
|------|------|----------|--------|----------|
| DuckDNS | 免费 | 简单 | 高 | ⭐⭐⭐⭐⭐ |
| Ngrok | 免费版有限制 | 简单 | 高 | ⭐⭐⭐⭐ |
| Serveo | 免费 | 中等 | 中等 | ⭐⭐⭐ |
| Cloudflare Tunnel | 免费 | 复杂 | 很高 | ⭐⭐⭐⭐⭐ |

## 安全建议

1. **定期更换Token**：虽然不是必须的，但建议定期更换DuckDNS token
2. **监控访问日志**：定期检查代理服务器的访问日志
3. **限制访问**：考虑添加IP白名单或认证机制
4. **使用HTTPS**：为您的域名配置SSL证书，启用HTTPS访问

## 技术支持

如果遇到问题，可以：
1. 查看代理服务器的日志文件
2. 检查DuckDNS官网的状态页面
3. 在项目的GitHub页面提交issue
