@echo off
echo 正在配置Windows防火墙允许9080端口...
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 检测到管理员权限，继续配置...
) else (
    echo 错误：需要管理员权限才能配置防火墙
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo.
echo 正在添加防火墙入站规则...

REM 删除可能存在的旧规则
netsh advfirewall firewall delete rule name="LMArena Proxy Server" >nul 2>&1

REM 添加新的入站规则
netsh advfirewall firewall add rule name="LMArena Proxy Server" dir=in action=allow protocol=TCP localport=9080

if %errorLevel% == 0 (
    echo ✅ 防火墙规则添加成功！
    echo.
    echo 已允许TCP端口9080的入站连接
    echo 规则名称: LMArena Proxy Server
) else (
    echo ❌ 防火墙规则添加失败
    echo 请检查是否有足够的权限
)

echo.
echo 当前防火墙状态:
netsh advfirewall show allprofiles state

echo.
echo 配置完成！
echo 现在请配置路由器端口转发，然后测试访问 http://ll555.duckdns.org:9080
pause
