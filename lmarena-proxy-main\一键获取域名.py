#!/usr/bin/env python3
"""
一键获取免费公网域名
支持多种内网穿透方案，自动选择最佳方案
"""

import subprocess
import sys
import os
import time
import requests

def check_proxy_server():
    """检查代理服务器是否运行"""
    try:
        response = requests.get("http://localhost:9080/health", timeout=5)
        if response.status_code == 200:
            print("✅ 代理服务器运行正常")
            return True
        else:
            print("❌ 代理服务器响应异常")
            return False
    except Exception as e:
        print("❌ 代理服务器未运行")
        print("请先运行: python proxy_server.py")
        return False

def check_tool_availability():
    """检查各种工具的可用性"""
    tools = {}
    
    # 检查ngrok
    try:
        subprocess.run(["ngrok", "version"], capture_output=True, check=True)
        tools['ngrok'] = True
    except:
        tools['ngrok'] = False
    
    # 检查localtunnel (需要node)
    try:
        subprocess.run(["node", "--version"], capture_output=True, check=True)
        try:
            subprocess.run(["lt", "--version"], capture_output=True, check=True)
            tools['localtunnel'] = True
        except:
            tools['localtunnel'] = 'need_install'
    except:
        tools['localtunnel'] = False
    
    # 检查SSH (Serveo)
    try:
        subprocess.run(["ssh", "-V"], capture_output=True, check=True)
        tools['serveo'] = True
    except:
        tools['serveo'] = False
    
    return tools

def show_menu(tools):
    """显示选择菜单"""
    print("\n🌐 可用的免费域名方案:")
    print("=" * 50)
    
    options = []
    
    # Ngrok选项
    if tools['ngrok']:
        print("1. 🚀 Ngrok (推荐)")
        print("   ✅ 已安装，稳定快速")
        print("   📝 免费版有时间限制，可注册获得更多功能")
        options.append(('ngrok', '使用Ngrok.py'))
    else:
        print("1. 🚀 Ngrok")
        print("   ❌ 未安装，需要下载")
        print("   📝 最流行的内网穿透工具")
        options.append(('ngrok', '使用Ngrok.py'))
    
    # LocalTunnel选项
    if tools['localtunnel'] == True:
        print("\n2. 🌐 LocalTunnel")
        print("   ✅ 已安装，完全免费")
        print("   📝 支持自定义子域名")
        options.append(('localtunnel', '使用LocalTunnel.py'))
    elif tools['localtunnel'] == 'need_install':
        print("\n2. 🌐 LocalTunnel")
        print("   ⚠️  Node.js已安装，需要安装LocalTunnel")
        print("   📝 完全免费，支持自定义子域名")
        options.append(('localtunnel', '使用LocalTunnel.py'))
    else:
        print("\n2. 🌐 LocalTunnel")
        print("   ❌ 需要Node.js环境")
        print("   📝 完全免费，但需要先安装Node.js")
        options.append(('localtunnel', '使用LocalTunnel.py'))
    
    # Serveo选项
    if tools['serveo']:
        print("\n3. 🔗 Serveo")
        print("   ✅ SSH可用，无需安装")
        print("   📝 基于SSH隧道，稳定可靠")
        options.append(('serveo', '使用Serveo.py'))
    else:
        print("\n3. 🔗 Serveo")
        print("   ❌ SSH不可用")
        print("   📝 需要SSH客户端")
        options.append(('serveo', '使用Serveo.py'))
    
    print("\n4. 🔧 手动配置DuckDNS")
    print("   📝 需要路由器端口转发配置")
    options.append(('duckdns', '配置DuckDNS.py'))
    
    print("\n0. ❌ 退出")
    
    return options

def run_selected_tool(script_name):
    """运行选择的工具"""
    try:
        print(f"\n🚀 正在启动 {script_name}...")
        subprocess.run([sys.executable, script_name], check=True)
    except subprocess.CalledProcessError:
        print(f"❌ {script_name} 运行失败")
    except KeyboardInterrupt:
        print(f"\n🛑 {script_name} 已停止")
    except Exception as e:
        print(f"❌ 运行出错: {e}")

def recommend_best_option(tools):
    """推荐最佳选项"""
    if tools['ngrok']:
        return "推荐使用 Ngrok (选项1) - 最稳定可靠"
    elif tools['localtunnel'] == True:
        return "推荐使用 LocalTunnel (选项2) - 完全免费"
    elif tools['serveo']:
        return "推荐使用 Serveo (选项3) - 无需安装"
    else:
        return "推荐先安装 Ngrok 或 Node.js"

def main():
    print("🎯 一键获取免费公网域名")
    print("=" * 50)
    print("自动检测可用工具，选择最佳方案")
    
    # 检查代理服务器
    if not check_proxy_server():
        input("按回车键退出...")
        return
    
    # 检查工具可用性
    print("\n🔍 正在检测可用工具...")
    tools = check_tool_availability()
    
    # 显示菜单
    options = show_menu(tools)
    
    # 显示推荐
    print(f"\n💡 {recommend_best_option(tools)}")
    
    # 获取用户选择
    while True:
        try:
            choice = input("\n请选择方案 (0-4): ").strip()
            
            if choice == '0':
                print("👋 再见！")
                return
            
            choice_num = int(choice)
            if 1 <= choice_num <= len(options):
                tool_name, script_name = options[choice_num - 1]
                
                # 确认选择
                print(f"\n您选择了: {tool_name}")
                confirm = input("确认启动吗？(Y/n): ").strip().lower()
                
                if confirm in ['', 'y', 'yes']:
                    run_selected_tool(script_name)
                    break
                else:
                    continue
            else:
                print("❌ 无效选择，请重新输入")
                
        except ValueError:
            print("❌ 请输入数字")
        except KeyboardInterrupt:
            print("\n\n👋 再见！")
            return

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ 操作已取消")
    except Exception as e:
        print(f"\n❌ 出现错误: {e}")
        input("按回车键退出...")
