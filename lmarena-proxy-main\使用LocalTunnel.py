#!/usr/bin/env python3
"""
使用LocalTunnel获取免费公网域名
完全免费，支持自定义子域名
"""

import subprocess
import sys
import os
import time
import requests
import json

def check_node_installed():
    """检查Node.js是否已安装"""
    try:
        result = subprocess.run(["node", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js已安装: {result.stdout.strip()}")
            return True
        else:
            return False
    except FileNotFoundError:
        return False

def check_localtunnel_installed():
    """检查localtunnel是否已安装"""
    try:
        result = subprocess.run(["lt", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ LocalTunnel已安装: {result.stdout.strip()}")
            return True
        else:
            return False
    except FileNotFoundError:
        return False

def install_localtunnel():
    """安装localtunnel"""
    print("📥 正在安装LocalTunnel...")
    
    try:
        result = subprocess.run(["npm", "install", "-g", "localtunnel"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ LocalTunnel安装成功")
            return True
        else:
            print(f"❌ 安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 安装出错: {e}")
        return False

def start_localtunnel():
    """启动LocalTunnel"""
    print("\n🚇 正在启动LocalTunnel...")
    
    # 询问是否要自定义子域名
    print("LocalTunnel支持自定义子域名（可选）")
    subdomain = input("请输入子域名（留空使用随机域名）: ").strip()
    
    try:
        # 构建命令
        cmd = ["lt", "--port", "9080"]
        if subdomain:
            cmd.extend(["--subdomain", subdomain])
            print(f"尝试使用子域名: {subdomain}")
        
        print("启动命令:", " ".join(cmd))
        print("请等待几秒钟...")
        
        # 启动localtunnel
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # 等待并读取输出
        public_url = None
        for i in range(10):  # 最多等待10秒
            time.sleep(1)
            if process.poll() is not None:
                # 进程已结束，读取错误信息
                stdout, stderr = process.communicate()
                print(f"❌ LocalTunnel启动失败")
                if stderr:
                    print(f"错误信息: {stderr}")
                if stdout:
                    print(f"输出信息: {stdout}")
                return False
            
            # 尝试读取输出
            try:
                line = process.stdout.readline()
                if line and "https://" in line:
                    import re
                    match = re.search(r'https://[^\s]+', line)
                    if match:
                        public_url = match.group(0)
                        break
            except:
                continue
        
        if public_url:
            print(f"\n🎉 LocalTunnel启动成功！")
            print(f"🌐 公网访问地址: {public_url}")
            print(f"📊 监控面板: {public_url}/monitor")
            
            # 更新代理服务器配置
            update_proxy_config(public_url)
            
            print(f"\n✨ 现在您可以通过以下地址访问:")
            print(f"   API: {public_url}/v1/chat/completions")
            print(f"   监控: {public_url}/monitor")
            
            print(f"\n⚠️  注意:")
            print(f"   - LocalTunnel完全免费")
            print(f"   - 支持自定义子域名")
            print(f"   - 按Ctrl+C停止隧道")
            
            # 保持运行
            try:
                print("\n隧道正在运行中...")
                process.wait()
            except KeyboardInterrupt:
                print("\n🛑 正在停止隧道...")
                process.terminate()
                print("✅ 隧道已停止")
            
            return True
        else:
            print("❌ 无法获取公网URL")
            process.terminate()
            return False
        
    except Exception as e:
        print(f"❌ 启动LocalTunnel失败: {e}")
        return False

def update_proxy_config(public_url):
    """更新代理服务器配置"""
    try:
        config_data = {
            "tunnel": {
                "enabled": True,
                "type": "localtunnel",
                "public_url": public_url
            }
        }
        
        response = requests.post(
            "http://localhost:9080/api/config",
            json=config_data,
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        
        if response.status_code == 200:
            print("✅ 代理服务器配置已更新")
        else:
            print("⚠️  代理服务器配置更新失败，但不影响使用")
            
    except Exception as e:
        print(f"⚠️  配置更新出错: {e}")

def check_proxy_server():
    """检查代理服务器是否运行"""
    try:
        response = requests.get("http://localhost:9080/health", timeout=5)
        if response.status_code == 200:
            print("✅ 代理服务器运行正常")
            return True
        else:
            print("❌ 代理服务器响应异常")
            return False
    except Exception as e:
        print("❌ 代理服务器未运行，请先启动代理服务器")
        print("运行命令: python proxy_server.py")
        return False

def install_nodejs():
    """安装Node.js"""
    print("❌ 未检测到Node.js")
    print("LocalTunnel需要Node.js环境")
    print("\n请手动安装Node.js:")
    print("1. 访问 https://nodejs.org")
    print("2. 下载并安装LTS版本")
    print("3. 重启命令行后重试")
    
    choice = input("\n是否要打开Node.js官网？(y/N): ").strip().lower()
    if choice == 'y':
        import webbrowser
        webbrowser.open("https://nodejs.org")

def main():
    print("🌐 LocalTunnel快速部署工具")
    print("=" * 50)
    print("LocalTunnel是完全免费的内网穿透方案")
    
    # 检查代理服务器
    if not check_proxy_server():
        return
    
    # 检查Node.js
    if not check_node_installed():
        install_nodejs()
        return
    
    # 检查LocalTunnel
    if not check_localtunnel_installed():
        print("❌ 未检测到LocalTunnel")
        if input("是否要自动安装？(y/N): ").strip().lower() == 'y':
            if not install_localtunnel():
                return
        else:
            print("请手动安装: npm install -g localtunnel")
            return
    
    # 启动隧道
    start_localtunnel()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ 操作已取消")
    except Exception as e:
        print(f"\n❌ 出现错误: {e}")
        input("按回车键退出...")
