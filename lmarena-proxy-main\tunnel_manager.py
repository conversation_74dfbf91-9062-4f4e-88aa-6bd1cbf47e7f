import asyncio
import logging
import subprocess
import json
import time
import aiohttp
import os
import signal
from enum import Enum
from dataclasses import dataclass
from typing import Optional, Dict, Any
from pathlib import Path


class TunnelType(Enum):
    NGROK = "ngrok"
    NGROK_OPEN = "ngrok_open"
    SERVEO = "serveo"
    LOCALHOST_RUN = "localhost_run"
    LOCALTUNNEL = "localtunnel"
    CLOUDFLARE = "cloudflare"
    FRP = "frp"
    DUCKDNS = "duckdns"  # 新增DuckDNS支持


@dataclass
class TunnelConfig:
    enabled: bool = False
    tunnel_type: TunnelType = TunnelType.NGROK
    local_port: int = 9080
    auth_token: str = ""
    subdomain: str = ""
    region: str = "us"
    custom_domain: str = ""
    frp_server: str = ""
    frp_port: int = 7000
    frp_token: str = ""
    cloudflare_tunnel_token: str = ""
    # DuckDNS配置
    duckdns_domain: str = ""  # 如 "ll555.duckdns.org"
    duckdns_token: str = ""   # DuckDNS token


class TunnelManager:
    def __init__(self, config: TunnelConfig):
        self.config = config
        self.process: Optional[subprocess.Popen] = None
        self.public_url: Optional[str] = None
        self.error_message: Optional[str] = None
        self.is_running = False
        self.log_file: Optional[Path] = None

    async def start_tunnel(self) -> bool:
        """启动内网穿透隧道"""
        if self.is_running:
            logging.warning("隧道已在运行中")
            return True

        try:
            if self.config.tunnel_type == TunnelType.DUCKDNS:
                return await self._start_duckdns()
            elif self.config.tunnel_type == TunnelType.NGROK:
                return await self._start_ngrok()
            elif self.config.tunnel_type == TunnelType.NGROK_OPEN:
                return await self._start_ngrok_open()
            elif self.config.tunnel_type == TunnelType.SERVEO:
                return await self._start_serveo()
            elif self.config.tunnel_type == TunnelType.LOCALHOST_RUN:
                return await self._start_localhost_run()
            elif self.config.tunnel_type == TunnelType.LOCALTUNNEL:
                return await self._start_localtunnel()
            elif self.config.tunnel_type == TunnelType.CLOUDFLARE:
                return await self._start_cloudflare()
            elif self.config.tunnel_type == TunnelType.FRP:
                return await self._start_frp()
            else:
                self.error_message = f"不支持的隧道类型: {self.config.tunnel_type}"
                return False

        except Exception as e:
            self.error_message = f"启动隧道失败: {str(e)}"
            logging.error(self.error_message, exc_info=True)
            return False

    async def _start_duckdns(self) -> bool:
        """启动DuckDNS动态域名更新"""
        if not self.config.duckdns_domain or not self.config.duckdns_token:
            self.error_message = "DuckDNS域名和token不能为空"
            return False

        try:
            # 获取当前公网IP
            public_ip = await self._get_public_ip()
            if not public_ip:
                self.error_message = "无法获取公网IP地址"
                return False

            # 更新DuckDNS记录
            domain_name = self.config.duckdns_domain.replace('.duckdns.org', '')
            update_url = f"https://www.duckdns.org/update?domains={domain_name}&token={self.config.duckdns_token}&ip={public_ip}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(update_url) as response:
                    result = await response.text()
                    if result.strip() == "OK":
                        self.public_url = f"http://{self.config.duckdns_domain}:{self.config.local_port}"
                        self.is_running = True
                        logging.info(f"DuckDNS更新成功: {self.config.duckdns_domain} -> {public_ip}")
                        
                        # 启动定期更新任务
                        asyncio.create_task(self._duckdns_update_loop())
                        return True
                    else:
                        self.error_message = f"DuckDNS更新失败: {result}"
                        return False

        except Exception as e:
            self.error_message = f"DuckDNS配置失败: {str(e)}"
            return False

    async def _duckdns_update_loop(self):
        """DuckDNS定期更新循环"""
        while self.is_running:
            try:
                await asyncio.sleep(300)  # 每5分钟更新一次
                if not self.is_running:
                    break
                    
                public_ip = await self._get_public_ip()
                if public_ip:
                    domain_name = self.config.duckdns_domain.replace('.duckdns.org', '')
                    update_url = f"https://www.duckdns.org/update?domains={domain_name}&token={self.config.duckdns_token}&ip={public_ip}"
                    
                    async with aiohttp.ClientSession() as session:
                        async with session.get(update_url) as response:
                            result = await response.text()
                            if result.strip() == "OK":
                                logging.debug(f"DuckDNS定期更新成功: {self.config.duckdns_domain}")
                            else:
                                logging.warning(f"DuckDNS定期更新失败: {result}")
                                
            except Exception as e:
                logging.error(f"DuckDNS定期更新出错: {e}")

    async def _get_public_ip(self) -> Optional[str]:
        """获取公网IP地址"""
        try:
            async with aiohttp.ClientSession() as session:
                # 尝试多个IP查询服务
                services = [
                    "https://api.ipify.org",
                    "https://ifconfig.me/ip",
                    "https://icanhazip.com"
                ]
                
                for service in services:
                    try:
                        async with session.get(service, timeout=10) as response:
                            if response.status == 200:
                                ip = (await response.text()).strip()
                                if ip and '.' in ip:  # 简单验证IP格式
                                    return ip
                    except:
                        continue
                        
        except Exception as e:
            logging.error(f"获取公网IP失败: {e}")
        
        return None

    async def _start_ngrok(self) -> bool:
        """启动Ngrok隧道"""
        if not self.config.auth_token:
            self.error_message = "Ngrok需要auth_token"
            return False

        cmd = ["ngrok", "http", str(self.config.local_port)]
        if self.config.subdomain:
            cmd.extend(["--subdomain", self.config.subdomain])
        if self.config.region:
            cmd.extend(["--region", self.config.region])

        return await self._start_process(cmd, "ngrok")

    async def _start_ngrok_open(self) -> bool:
        """启动Ngrok开源版"""
        cmd = ["ngrok", "http", str(self.config.local_port)]
        return await self._start_process(cmd, "ngrok_open")

    async def _start_serveo(self) -> bool:
        """启动Serveo SSH隧道"""
        subdomain_arg = f"{self.config.subdomain}:" if self.config.subdomain else ""
        cmd = [
            "ssh", "-o", "StrictHostKeyChecking=no", "-o", "ServerAliveInterval=60",
            "-R", f"{subdomain_arg}{self.config.local_port}:localhost:{self.config.local_port}",
            "serveo.net"
        ]
        return await self._start_process(cmd, "serveo")

    async def _start_localhost_run(self) -> bool:
        """启动localhost.run SSH隧道"""
        cmd = [
            "ssh", "-o", "StrictHostKeyChecking=no", "-o", "ServerAliveInterval=60",
            "-R", f"80:localhost:{self.config.local_port}",
            "localhost.run"
        ]
        return await self._start_process(cmd, "localhost_run")

    async def _start_localtunnel(self) -> bool:
        """启动LocalTunnel"""
        cmd = ["lt", "--port", str(self.config.local_port)]
        if self.config.subdomain:
            cmd.extend(["--subdomain", self.config.subdomain])
        return await self._start_process(cmd, "localtunnel")

    async def _start_cloudflare(self) -> bool:
        """启动Cloudflare Tunnel"""
        if not self.config.cloudflare_tunnel_token:
            self.error_message = "Cloudflare Tunnel需要token"
            return False

        cmd = ["cloudflared", "tunnel", "run", "--token", self.config.cloudflare_tunnel_token]
        return await self._start_process(cmd, "cloudflare")

    async def _start_frp(self) -> bool:
        """启动FRP客户端"""
        if not self.config.frp_server:
            self.error_message = "FRP需要服务器地址"
            return False

        # 创建FRP配置文件
        frp_config = {
            "serverAddr": self.config.frp_server,
            "serverPort": self.config.frp_port,
            "auth": {"token": self.config.frp_token} if self.config.frp_token else {},
            "proxies": [{
                "name": "web",
                "type": "http",
                "localPort": self.config.local_port,
                "customDomains": [self.config.custom_domain] if self.config.custom_domain else []
            }]
        }

        config_file = Path("frpc.json")
        with open(config_file, 'w') as f:
            json.dump(frp_config, f, indent=2)

        cmd = ["frpc", "-c", str(config_file)]
        return await self._start_process(cmd, "frp")

    async def _start_process(self, cmd: list, service_name: str) -> bool:
        """启动进程并监控输出"""
        try:
            self.log_file = Path(f"logs/tunnel_{service_name}.log")
            self.log_file.parent.mkdir(exist_ok=True)

            with open(self.log_file, 'w') as log_f:
                self.process = subprocess.Popen(
                    cmd,
                    stdout=log_f,
                    stderr=subprocess.STDOUT,
                    text=True,
                    bufsize=1
                )

            # 等待进程启动并解析URL
            await asyncio.sleep(3)
            
            if self.process.poll() is not None:
                self.error_message = f"{service_name}进程启动失败"
                return False

            # 尝试从日志中解析公网URL
            self.public_url = await self._parse_tunnel_url(service_name)
            if self.public_url:
                self.is_running = True
                logging.info(f"{service_name}隧道启动成功: {self.public_url}")
                return True
            else:
                self.error_message = f"无法获取{service_name}的公网URL"
                return False

        except Exception as e:
            self.error_message = f"启动{service_name}失败: {str(e)}"
            return False

    async def _parse_tunnel_url(self, service_name: str) -> Optional[str]:
        """从日志中解析隧道URL"""
        if not self.log_file or not self.log_file.exists():
            return None

        try:
            with open(self.log_file, 'r') as f:
                content = f.read()

            if service_name == "ngrok" or service_name == "ngrok_open":
                # 解析ngrok URL
                import re
                match = re.search(r'https://[a-zA-Z0-9-]+\.ngrok\.io', content)
                if match:
                    return match.group(0)
            elif service_name == "serveo":
                # 解析serveo URL
                import re
                match = re.search(r'https://[a-zA-Z0-9-]+\.serveo\.net', content)
                if match:
                    return match.group(0)
            elif service_name == "localhost_run":
                # 解析localhost.run URL
                import re
                match = re.search(r'https://[a-zA-Z0-9-]+\.localhost\.run', content)
                if match:
                    return match.group(0)

        except Exception as e:
            logging.error(f"解析{service_name} URL失败: {e}")

        return None

    async def stop_tunnel(self):
        """停止隧道"""
        self.is_running = False
        
        if self.process:
            try:
                self.process.terminate()
                await asyncio.sleep(2)
                if self.process.poll() is None:
                    self.process.kill()
                self.process = None
                logging.info("隧道进程已停止")
            except Exception as e:
                logging.error(f"停止隧道进程失败: {e}")

        self.public_url = None
        self.error_message = None

    def get_status(self) -> Dict[str, Any]:
        """获取隧道状态"""
        return {
            "is_running": self.is_running,
            "public_url": self.public_url,
            "error_message": self.error_message,
            "tunnel_type": self.config.tunnel_type.value,
            "local_port": self.config.local_port
        }
