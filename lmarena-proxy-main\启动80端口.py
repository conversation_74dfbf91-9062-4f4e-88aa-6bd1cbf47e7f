#!/usr/bin/env python3
"""
在80端口启动代理服务器
这样可以直接通过 http://ll555.duckdns.org 访问，无需端口号
"""

import subprocess
import sys
import os
import time
import requests

def check_admin_rights():
    """检查是否有管理员权限"""
    try:
        import ctypes
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def kill_existing_server():
    """停止现有的服务器"""
    print("🔄 正在停止现有的代理服务器...")
    try:
        # 尝试优雅停止
        response = requests.post("http://localhost:9080/api/shutdown", timeout=5)
        time.sleep(2)
    except:
        pass
    
    # 强制停止Python进程
    try:
        subprocess.run(["taskkill", "/f", "/im", "python.exe"], 
                      capture_output=True, check=False)
        time.sleep(1)
    except:
        pass

def update_config_for_port_80():
    """更新配置文件，将端口改为80"""
    print("⚙️ 正在更新配置为80端口...")
    
    # 读取当前配置
    config_file = "logs/config.json"
    config = {}
    
    if os.path.exists(config_file):
        try:
            import json
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
        except:
            pass
    
    # 更新端口配置
    if 'network' not in config:
        config['network'] = {}
    config['network']['port'] = 80
    
    if 'tunnel' not in config:
        config['tunnel'] = {}
    config['tunnel']['enabled'] = True
    config['tunnel']['type'] = 'duckdns'
    config['tunnel']['duckdns_domain'] = 'll555.duckdns.org'
    config['tunnel']['duckdns_token'] = '2ac70818-cacc-48a1-9dc1-7360875ccc72'
    
    # 保存配置
    os.makedirs("logs", exist_ok=True)
    try:
        import json
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        print("✅ 配置已更新")
    except Exception as e:
        print(f"❌ 配置更新失败: {e}")

def start_server_on_port_80():
    """在80端口启动服务器"""
    print("🚀 正在80端口启动代理服务器...")
    
    # 修改proxy_server.py中的端口配置
    try:
        with open("proxy_server.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 临时修改端口
        content = content.replace("PORT = 9080", "PORT = 80")
        
        with open("proxy_server_80.py", "w", encoding="utf-8") as f:
            f.write(content)
        
        print("✅ 已创建80端口版本的服务器文件")
        
        # 启动服务器
        print("🌐 启动服务器...")
        print("   访问地址: http://ll555.duckdns.org")
        print("   监控面板: http://ll555.duckdns.org/monitor")
        print("   按 Ctrl+C 停止服务器")
        
        subprocess.run([sys.executable, "proxy_server_80.py"])
        
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def main():
    print("🌐 80端口代理服务器启动器")
    print("=" * 50)
    
    # 检查管理员权限
    if not check_admin_rights():
        print("❌ 需要管理员权限才能使用80端口")
        print("请右键点击此文件，选择'以管理员身份运行'")
        input("按回车键退出...")
        return
    
    print("✅ 检测到管理员权限")
    
    # 停止现有服务器
    kill_existing_server()
    
    # 更新配置
    update_config_for_port_80()
    
    # 启动80端口服务器
    start_server_on_port_80()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ 启动已取消")
    except Exception as e:
        print(f"\n❌ 启动过程中出现错误: {e}")
        input("按回车键退出...")
