#!/usr/bin/env python3
"""
使用Serveo SSH隧道获取免费公网域名
无需安装，只需要SSH客户端
"""

import subprocess
import sys
import os
import time
import requests
import threading
import re

def check_ssh_available():
    """检查SSH客户端是否可用"""
    try:
        result = subprocess.run(["ssh", "-V"], capture_output=True, text=True)
        print("✅ SSH客户端可用")
        return True
    except FileNotFoundError:
        print("❌ 未找到SSH客户端")
        print("请安装OpenSSH或使用Git Bash")
        return False

def start_serveo_tunnel():
    """启动Serveo隧道"""
    print("\n🚇 正在启动Serveo隧道...")
    
    # 询问是否要自定义子域名
    print("Serveo支持自定义子域名（可选）")
    subdomain = input("请输入子域名（留空使用随机域名）: ").strip()
    
    try:
        # 构建SSH命令
        if subdomain:
            ssh_cmd = [
                "ssh", "-o", "StrictHostKeyChecking=no", 
                "-o", "ServerAliveInterval=60",
                "-R", f"{subdomain}:80:localhost:9080",
                "serveo.net"
            ]
            print(f"尝试使用子域名: {subdomain}")
        else:
            ssh_cmd = [
                "ssh", "-o", "StrictHostKeyChecking=no",
                "-o", "ServerAliveInterval=60", 
                "-R", "80:localhost:9080",
                "serveo.net"
            ]
        
        print("启动命令:", " ".join(ssh_cmd))
        print("请等待连接...")
        
        # 启动SSH隧道
        process = subprocess.Popen(
            ssh_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # 读取输出获取公网URL
        public_url = None
        for i in range(30):  # 最多等待30秒
            if process.poll() is not None:
                print("❌ SSH连接失败")
                return False
            
            try:
                line = process.stdout.readline()
                if line:
                    print(f"Serveo: {line.strip()}")
                    
                    # 查找URL
                    if "https://" in line and "serveo.net" in line:
                        match = re.search(r'https://[^\s]+\.serveo\.net', line)
                        if match:
                            public_url = match.group(0)
                            break
                    elif "Forwarding HTTP traffic from" in line:
                        match = re.search(r'https://[^\s]+', line)
                        if match:
                            public_url = match.group(0)
                            break
            except:
                time.sleep(1)
                continue
        
        if public_url:
            print(f"\n🎉 Serveo隧道启动成功！")
            print(f"🌐 公网访问地址: {public_url}")
            print(f"📊 监控面板: {public_url}/monitor")
            
            # 更新代理服务器配置
            update_proxy_config(public_url)
            
            print(f"\n✨ 现在您可以通过以下地址访问:")
            print(f"   API: {public_url}/v1/chat/completions")
            print(f"   监控: {public_url}/monitor")
            
            print(f"\n⚠️  注意:")
            print(f"   - Serveo完全免费")
            print(f"   - 基于SSH隧道，稳定可靠")
            print(f"   - 按Ctrl+C停止隧道")
            
            # 保持运行
            try:
                print("\n隧道正在运行中...")
                # 继续读取输出以保持连接
                while process.poll() is None:
                    line = process.stdout.readline()
                    if line:
                        print(f"Serveo: {line.strip()}")
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n🛑 正在停止隧道...")
                process.terminate()
                print("✅ 隧道已停止")
            
            return True
        else:
            print("❌ 无法获取公网URL")
            print("可能的原因:")
            print("1. 网络连接问题")
            print("2. Serveo服务暂时不可用")
            print("3. 子域名已被占用")
            process.terminate()
            return False
        
    except Exception as e:
        print(f"❌ 启动Serveo失败: {e}")
        return False

def update_proxy_config(public_url):
    """更新代理服务器配置"""
    try:
        config_data = {
            "tunnel": {
                "enabled": True,
                "type": "serveo",
                "public_url": public_url
            }
        }
        
        response = requests.post(
            "http://localhost:9080/api/config",
            json=config_data,
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        
        if response.status_code == 200:
            print("✅ 代理服务器配置已更新")
        else:
            print("⚠️  代理服务器配置更新失败，但不影响使用")
            
    except Exception as e:
        print(f"⚠️  配置更新出错: {e}")

def check_proxy_server():
    """检查代理服务器是否运行"""
    try:
        response = requests.get("http://localhost:9080/health", timeout=5)
        if response.status_code == 200:
            print("✅ 代理服务器运行正常")
            return True
        else:
            print("❌ 代理服务器响应异常")
            return False
    except Exception as e:
        print("❌ 代理服务器未运行，请先启动代理服务器")
        print("运行命令: python proxy_server.py")
        return False

def main():
    print("🔗 Serveo SSH隧道工具")
    print("=" * 50)
    print("Serveo是基于SSH的免费内网穿透方案")
    
    # 检查代理服务器
    if not check_proxy_server():
        return
    
    # 检查SSH
    if not check_ssh_available():
        return
    
    # 启动隧道
    start_serveo_tunnel()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ 操作已取消")
    except Exception as e:
        print(f"\n❌ 出现错误: {e}")
        input("按回车键退出...")
