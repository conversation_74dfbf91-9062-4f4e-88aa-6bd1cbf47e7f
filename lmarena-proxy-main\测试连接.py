#!/usr/bin/env python3
"""
连接测试工具
帮助诊断DuckDNS和代理服务器的连接问题
"""

import requests
import socket
import subprocess
import sys
import time

def test_local_server():
    """测试本地服务器"""
    print("🔍 测试本地服务器...")
    try:
        response = requests.get("http://localhost:9080/health", timeout=5)
        if response.status_code == 200:
            print("✅ 本地服务器运行正常")
            return True
        else:
            print(f"❌ 本地服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接本地服务器: {e}")
        return False

def test_network_server():
    """测试局域网访问"""
    print("\n🔍 测试局域网访问...")
    try:
        response = requests.get("http://*************:9080/health", timeout=5)
        if response.status_code == 200:
            print("✅ 局域网访问正常")
            return True
        else:
            print(f"❌ 局域网访问异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 局域网访问失败: {e}")
        return False

def test_duckdns_resolution():
    """测试DuckDNS域名解析"""
    print("\n🔍 测试DuckDNS域名解析...")
    try:
        ip = socket.gethostbyname("ll555.duckdns.org")
        print(f"✅ 域名解析成功: ll555.duckdns.org → {ip}")
        
        # 获取当前公网IP
        try:
            public_ip = requests.get("https://api.ipify.org", timeout=10).text.strip()
            if ip == public_ip:
                print(f"✅ 域名指向正确的公网IP: {public_ip}")
                return True
            else:
                print(f"⚠️  域名指向的IP ({ip}) 与当前公网IP ({public_ip}) 不匹配")
                print("   这可能是DNS缓存问题，请等待几分钟后重试")
                return False
        except:
            print(f"✅ 域名解析到: {ip} (无法验证是否为当前公网IP)")
            return True
            
    except Exception as e:
        print(f"❌ 域名解析失败: {e}")
        return False

def test_port_accessibility():
    """测试端口可访问性"""
    print("\n🔍 测试端口可访问性...")
    
    # 测试本地端口
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex(('127.0.0.1', 9080))
        sock.close()
        
        if result == 0:
            print("✅ 本地端口9080可访问")
        else:
            print("❌ 本地端口9080不可访问")
            return False
    except Exception as e:
        print(f"❌ 端口测试失败: {e}")
        return False
    
    return True

def check_firewall():
    """检查防火墙设置"""
    print("\n🔍 检查防火墙设置...")
    try:
        # 检查防火墙规则
        result = subprocess.run([
            'netsh', 'advfirewall', 'firewall', 'show', 'rule', 
            'name="LMArena Proxy Server"'
        ], capture_output=True, text=True, timeout=10)
        
        if "LMArena Proxy Server" in result.stdout:
            print("✅ 防火墙规则已配置")
            return True
        else:
            print("❌ 未找到防火墙规则")
            print("   请运行 '配置防火墙.bat' 来配置防火墙")
            return False
            
    except Exception as e:
        print(f"⚠️  无法检查防火墙设置: {e}")
        return None

def test_external_access():
    """测试外部访问"""
    print("\n🔍 测试外部访问...")
    print("⚠️  外部访问需要路由器端口转发配置")
    
    try:
        response = requests.get("http://ll555.duckdns.org:9080/health", timeout=10)
        if response.status_code == 200:
            print("✅ 外部访问成功！")
            return True
        else:
            print(f"❌ 外部访问失败: HTTP {response.status_code}")
            return False
    except requests.exceptions.ConnectTimeout:
        print("❌ 连接超时 - 可能是路由器端口转发未配置")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ 连接被拒绝 - 可能是路由器端口转发未配置")
        return False
    except Exception as e:
        print(f"❌ 外部访问失败: {e}")
        return False

def main():
    print("🔧 DuckDNS连接诊断工具")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        ("本地服务器", test_local_server),
        ("局域网访问", test_network_server),
        ("域名解析", test_duckdns_resolution),
        ("端口可访问性", test_port_accessibility),
        ("防火墙设置", check_firewall),
        ("外部访问", test_external_access)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name}测试出错: {e}")
            results[test_name] = False
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 测试结果总结:")
    
    for test_name, result in results.items():
        if result is True:
            status = "✅ 通过"
        elif result is False:
            status = "❌ 失败"
        else:
            status = "⚠️  未知"
        print(f"   {test_name}: {status}")
    
    # 建议
    print("\n💡 建议:")
    if not results.get("本地服务器", False):
        print("   1. 请确保代理服务器正在运行")
    
    if not results.get("防火墙设置", True):
        print("   2. 请以管理员身份运行 '配置防火墙.bat'")
    
    if not results.get("外部访问", False):
        print("   3. 请配置路由器端口转发:")
        print("      - 外部端口: 9080")
        print("      - 内部IP: *************")
        print("      - 内部端口: 9080")
        print("      - 协议: TCP")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ 测试已取消")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
