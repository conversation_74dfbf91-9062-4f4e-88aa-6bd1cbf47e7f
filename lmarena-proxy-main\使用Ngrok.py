#!/usr/bin/env python3
"""
使用Ngrok快速获取公网域名
无需路由器配置，一键启动
"""

import subprocess
import sys
import os
import time
import requests
import json
import re

def check_ngrok_installed():
    """检查ngrok是否已安装"""
    try:
        result = subprocess.run(["ngrok", "version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Ngrok已安装: {result.stdout.strip()}")
            return True
        else:
            return False
    except FileNotFoundError:
        return False

def install_ngrok():
    """安装ngrok"""
    print("📥 正在下载并安装Ngrok...")
    
    # 检查是否有winget
    try:
        subprocess.run(["winget", "--version"], capture_output=True, check=True)
        print("使用winget安装ngrok...")
        result = subprocess.run(["winget", "install", "ngrok"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Ngrok安装成功")
            return True
    except:
        pass
    
    # 检查是否有choco
    try:
        subprocess.run(["choco", "--version"], capture_output=True, check=True)
        print("使用chocolatey安装ngrok...")
        result = subprocess.run(["choco", "install", "ngrok", "-y"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Ngrok安装成功")
            return True
    except:
        pass
    
    print("❌ 自动安装失败")
    print("请手动安装ngrok:")
    print("1. 访问 https://ngrok.com/download")
    print("2. 下载Windows版本")
    print("3. 解压到当前目录或添加到PATH")
    return False

def setup_ngrok_auth():
    """设置ngrok认证（可选）"""
    print("\n🔑 Ngrok认证设置（可选）")
    print("免费版本有一些限制，但基本功能可用")
    
    choice = input("是否要设置ngrok认证token？(y/N): ").strip().lower()
    if choice == 'y':
        print("\n请访问 https://dashboard.ngrok.com/get-started/your-authtoken")
        print("注册免费账户并获取authtoken")
        token = input("请输入您的authtoken（或按回车跳过）: ").strip()
        
        if token:
            try:
                result = subprocess.run(["ngrok", "config", "add-authtoken", token], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    print("✅ Authtoken设置成功")
                    return True
                else:
                    print(f"❌ Authtoken设置失败: {result.stderr}")
            except Exception as e:
                print(f"❌ 设置出错: {e}")
    
    print("继续使用免费版本...")
    return False

def start_ngrok_tunnel():
    """启动ngrok隧道"""
    print("\n🚇 正在启动Ngrok隧道...")
    
    try:
        # 启动ngrok
        print("启动命令: ngrok http 9080")
        print("请等待几秒钟...")
        
        process = subprocess.Popen(
            ["ngrok", "http", "9080"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待ngrok启动
        time.sleep(3)
        
        # 获取ngrok状态
        try:
            response = requests.get("http://localhost:4040/api/tunnels", timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get("tunnels"):
                    tunnel = data["tunnels"][0]
                    public_url = tunnel["public_url"]
                    print(f"\n🎉 Ngrok隧道启动成功！")
                    print(f"🌐 公网访问地址: {public_url}")
                    print(f"📊 监控面板: {public_url}/monitor")
                    print(f"🔧 Ngrok管理界面: http://localhost:4040")
                    
                    # 更新代理服务器配置
                    update_proxy_config(public_url)
                    
                    print(f"\n✨ 现在您可以通过以下地址访问:")
                    print(f"   API: {public_url}/v1/chat/completions")
                    print(f"   监控: {public_url}/monitor")
                    
                    print(f"\n⚠️  注意:")
                    print(f"   - 免费版本的URL会在重启后改变")
                    print(f"   - 如需固定域名，请注册ngrok账户")
                    print(f"   - 按Ctrl+C停止隧道")
                    
                    # 保持运行
                    try:
                        process.wait()
                    except KeyboardInterrupt:
                        print("\n🛑 正在停止隧道...")
                        process.terminate()
                        print("✅ 隧道已停止")
                    
                    return True
                else:
                    print("❌ 未找到活跃的隧道")
            else:
                print("❌ 无法获取ngrok状态")
        except Exception as e:
            print(f"❌ 获取隧道信息失败: {e}")
            print("请检查ngrok是否正常启动")
        
        return False
        
    except Exception as e:
        print(f"❌ 启动ngrok失败: {e}")
        return False

def update_proxy_config(public_url):
    """更新代理服务器配置"""
    try:
        config_data = {
            "tunnel": {
                "enabled": True,
                "type": "ngrok",
                "public_url": public_url
            }
        }
        
        response = requests.post(
            "http://localhost:9080/api/config",
            json=config_data,
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        
        if response.status_code == 200:
            print("✅ 代理服务器配置已更新")
        else:
            print("⚠️  代理服务器配置更新失败，但不影响使用")
            
    except Exception as e:
        print(f"⚠️  配置更新出错: {e}")

def check_proxy_server():
    """检查代理服务器是否运行"""
    try:
        response = requests.get("http://localhost:9080/health", timeout=5)
        if response.status_code == 200:
            print("✅ 代理服务器运行正常")
            return True
        else:
            print("❌ 代理服务器响应异常")
            return False
    except Exception as e:
        print("❌ 代理服务器未运行，请先启动代理服务器")
        print("运行命令: python proxy_server.py")
        return False

def main():
    print("🚀 Ngrok快速部署工具")
    print("=" * 50)
    print("Ngrok是最简单的内网穿透方案，无需路由器配置")
    
    # 检查代理服务器
    if not check_proxy_server():
        return
    
    # 检查ngrok安装
    if not check_ngrok_installed():
        print("❌ 未检测到Ngrok")
        if input("是否要自动安装？(y/N): ").strip().lower() == 'y':
            if not install_ngrok():
                return
        else:
            print("请手动安装ngrok后重试")
            return
    
    # 设置认证（可选）
    setup_ngrok_auth()
    
    # 启动隧道
    start_ngrok_tunnel()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ 操作已取消")
    except Exception as e:
        print(f"\n❌ 出现错误: {e}")
        input("按回车键退出...")
