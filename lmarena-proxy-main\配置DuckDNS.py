#!/usr/bin/env python3
"""
DuckDNS 配置助手
帮助您快速配置DuckDNS动态域名服务
"""

import requests
import json
import sys
import time
from pathlib import Path

def main():
    print("🦆 DuckDNS 配置助手")
    print("=" * 50)
    
    # 获取用户输入
    print("\n请输入您的DuckDNS配置信息：")
    print("(您可以从 https://www.duckdns.org 获取这些信息)")
    
    domain = input("\n1. 请输入您的DuckDNS域名 (例如: ll555.duckdns.org): ").strip()
    if not domain:
        print("❌ 域名不能为空")
        return
    
    # 确保域名格式正确
    if not domain.endswith('.duckdns.org'):
        if '.' not in domain:
            domain = f"{domain}.duckdns.org"
        else:
            print("❌ 请输入正确的DuckDNS域名格式")
            return
    
    token = input("2. 请输入您的DuckDNS Token: ").strip()
    if not token:
        print("❌ Token不能为空")
        return
    
    print(f"\n📋 配置信息:")
    print(f"   域名: {domain}")
    print(f"   Token: {token[:8]}...{token[-8:] if len(token) > 16 else token}")
    
    confirm = input("\n确认配置信息正确吗? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 配置已取消")
        return
    
    # 测试DuckDNS连接
    print("\n🔍 正在测试DuckDNS连接...")
    if not test_duckdns(domain, token):
        print("❌ DuckDNS连接测试失败，请检查域名和Token")
        return
    
    print("✅ DuckDNS连接测试成功")
    
    # 配置代理服务器
    print("\n⚙️ 正在配置代理服务器...")
    if configure_proxy_server(domain, token):
        print("✅ 代理服务器配置成功")
        print(f"\n🌐 您的代理服务器现在可以通过以下地址访问:")
        print(f"   http://{domain}:9080")
        print(f"   监控面板: http://{domain}:9080/monitor")
        
        print(f"\n📝 注意事项:")
        print(f"   1. 确保您的路由器已设置端口转发 (外部9080 → 内部9080)")
        print(f"   2. 确保防火墙允许9080端口入站连接")
        print(f"   3. DuckDNS会每5分钟自动更新您的IP地址")
        
    else:
        print("❌ 代理服务器配置失败")

def test_duckdns(domain, token):
    """测试DuckDNS连接"""
    try:
        # 获取当前IP
        ip_response = requests.get("https://api.ipify.org", timeout=10)
        if ip_response.status_code != 200:
            print("❌ 无法获取公网IP地址")
            return False
        
        current_ip = ip_response.text.strip()
        print(f"   当前公网IP: {current_ip}")
        
        # 更新DuckDNS记录
        domain_name = domain.replace('.duckdns.org', '')
        update_url = f"https://www.duckdns.org/update?domains={domain_name}&token={token}&ip={current_ip}"
        
        response = requests.get(update_url, timeout=10)
        if response.status_code == 200 and response.text.strip() == "OK":
            print(f"   DuckDNS更新成功: {domain} → {current_ip}")
            return True
        else:
            print(f"   DuckDNS更新失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"   连接测试出错: {e}")
        return False

def configure_proxy_server(domain, token):
    """配置代理服务器"""
    try:
        # 检查代理服务器是否运行
        try:
            response = requests.get("http://localhost:9080/health", timeout=5)
            if response.status_code != 200:
                print("❌ 代理服务器未运行，请先启动代理服务器")
                return False
        except:
            print("❌ 无法连接到代理服务器，请确保服务器在localhost:9080运行")
            return False
        
        # 配置内网穿透
        config_data = {
            "tunnel": {
                "enabled": True,
                "type": "duckdns",
                "duckdns_domain": domain,
                "duckdns_token": token
            }
        }
        
        response = requests.post(
            "http://localhost:9080/api/config",
            json=config_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            print("   配置已保存到代理服务器")
            
            # 启动隧道
            print("   正在启动DuckDNS隧道...")
            time.sleep(1)
            
            tunnel_response = requests.post("http://localhost:9080/api/tunnel/start", timeout=30)
            if tunnel_response.status_code == 200:
                result = tunnel_response.json()
                print(f"   隧道启动成功: {result.get('public_url', domain)}")
                return True
            else:
                error_msg = tunnel_response.json().get('detail', '未知错误')
                print(f"   隧道启动失败: {error_msg}")
                return False
        else:
            print(f"   配置保存失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"   配置出错: {e}")
        return False

def show_help():
    """显示帮助信息"""
    print("""
🦆 DuckDNS 配置助手使用说明

1. 准备工作:
   - 访问 https://www.duckdns.org 注册账户
   - 创建一个域名 (如: ll555)
   - 获取您的token

2. 运行此脚本:
   python 配置DuckDNS.py

3. 按提示输入域名和token

4. 脚本会自动:
   - 测试DuckDNS连接
   - 配置代理服务器
   - 启动动态域名服务

5. 配置完成后，您可以通过域名访问代理服务器

注意: 请确保代理服务器已在localhost:9080运行
""")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        show_help()
    else:
        try:
            main()
        except KeyboardInterrupt:
            print("\n\n❌ 配置已取消")
        except Exception as e:
            print(f"\n❌ 配置过程中出现错误: {e}")
            print("请检查网络连接和输入信息是否正确")
