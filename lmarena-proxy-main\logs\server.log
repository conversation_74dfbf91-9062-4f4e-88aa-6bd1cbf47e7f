21:27:09.927 - INFO - [lifespan:1173] - 服务器正在启动...
21:27:09.928 - INFO - [lifespan:1178] - 🌐 Server access URLs:
21:27:09.929 - INFO - [lifespan:1179] -   - Local: http://localhost:9080
21:27:09.929 - INFO - [lifespan:1180] -   - Network: http://*************:9080
21:27:09.929 - INFO - [lifespan:1181] - 📱 Use the Network URL to access from your phone on the same WiFi
21:27:09.929 - INFO - [lifespan:1184] - 
📋 Available Endpoints:
21:27:09.929 - INFO - [lifespan:1185] -   🖥️  Monitor Dashboard: http://*************:9080/monitor
21:27:09.930 - INFO - [lifespan:1186] -      实时监控面板，查看系统状态、请求日志、性能指标
21:27:09.930 - INFO - [lifespan:1188] - 
  📊 Metrics & Health:
21:27:09.930 - INFO - [lifespan:1189] -      - Prometheus Metrics: http://*************:9080/metrics
21:27:09.931 - INFO - [lifespan:1190] -        Prometheus格式的性能指标，可接入Grafana
21:27:09.931 - INFO - [lifespan:1191] -      - Health Check: http://*************:9080/health
21:27:09.931 - INFO - [lifespan:1192] -        基础健康检查
21:27:09.931 - INFO - [lifespan:1193] -      - Detailed Health: http://*************:9080/api/health/detailed
21:27:09.931 - INFO - [lifespan:1194] -        详细健康状态，包含评分和建议
21:27:09.931 - INFO - [lifespan:1196] - 
  🤖 AI API:
21:27:09.933 - INFO - [lifespan:1197] -      - Chat Completions: POST http://*************:9080/v1/chat/completions
21:27:09.933 - INFO - [lifespan:1198] -        OpenAI兼容的聊天API
21:27:09.933 - INFO - [lifespan:1199] -      - List Models: GET http://*************:9080/v1/models
21:27:09.933 - INFO - [lifespan:1200] -        获取可用模型列表
21:27:09.934 - INFO - [lifespan:1201] -      - Refresh Models: POST http://*************:9080/v1/refresh-models
21:27:09.934 - INFO - [lifespan:1202] -        刷新模型列表
21:27:09.934 - INFO - [lifespan:1204] - 
  📈 Statistics:
21:27:09.934 - INFO - [lifespan:1205] -      - Stats Summary: http://*************:9080/api/stats/summary
21:27:09.934 - INFO - [lifespan:1206] -        24小时统计摘要
21:27:09.935 - INFO - [lifespan:1207] -      - Request Logs: http://*************:9080/api/logs/requests
21:27:09.935 - INFO - [lifespan:1208] -        请求日志API
21:27:09.935 - INFO - [lifespan:1209] -      - Error Logs: http://*************:9080/api/logs/errors
21:27:09.935 - INFO - [lifespan:1210] -        错误日志API
21:27:09.935 - INFO - [lifespan:1211] -      - Alerts: http://*************:9080/api/alerts
21:27:09.936 - INFO - [lifespan:1212] -        系统告警历史
21:27:09.936 - INFO - [lifespan:1214] - 
  🛠️  OpenAI Client Config:
21:27:09.936 - INFO - [lifespan:1215] -      base_url='http://*************:9080/v1'
21:27:09.936 - INFO - [lifespan:1216] -      api_key='sk-any-string-you-like'
21:27:09.936 - INFO - [lifespan:1217] - 
============================================================

21:27:09.936 - INFO - [lifespan:1222] - 已加载 85 个备用模型
21:27:09.936 - INFO - [lifespan:1231] - 服务器启动完成
21:27:09.938 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
21:32:09.933 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
21:32:39.980 - WARNING - [send_alert:660] - 系统告警: 浏览器已断线 5 分钟
21:33:10.007 - WARNING - [send_alert:660] - 系统告警: 浏览器已断线 5 分钟
21:33:40.009 - WARNING - [send_alert:660] - 系统告警: 浏览器已断线 6 分钟
21:34:10.015 - WARNING - [send_alert:660] - 系统告警: 浏览器已断线 6 分钟
21:34:40.028 - WARNING - [send_alert:660] - 系统告警: 浏览器已断线 7 分钟
21:35:10.039 - WARNING - [send_alert:660] - 系统告警: 浏览器已断线 7 分钟
21:35:40.059 - WARNING - [send_alert:660] - 系统告警: 浏览器已断线 8 分钟
21:36:10.061 - WARNING - [send_alert:660] - 系统告警: 浏览器已断线 8 分钟
21:36:37.766 - INFO - [lifespan:1173] - 服务器正在启动...
21:36:37.768 - INFO - [lifespan:1178] - 🌐 Server access URLs:
21:36:37.768 - INFO - [lifespan:1179] -   - Local: http://localhost:9080
21:36:37.768 - INFO - [lifespan:1180] -   - Network: http://*************:9080
21:36:37.768 - INFO - [lifespan:1181] - 📱 Use the Network URL to access from your phone on the same WiFi
21:36:37.769 - INFO - [lifespan:1184] - 
📋 Available Endpoints:
21:36:37.769 - INFO - [lifespan:1185] -   🖥️  Monitor Dashboard: http://*************:9080/monitor
21:36:37.769 - INFO - [lifespan:1186] -      实时监控面板，查看系统状态、请求日志、性能指标
21:36:37.769 - INFO - [lifespan:1188] - 
  📊 Metrics & Health:
21:36:37.769 - INFO - [lifespan:1189] -      - Prometheus Metrics: http://*************:9080/metrics
21:36:37.769 - INFO - [lifespan:1190] -        Prometheus格式的性能指标，可接入Grafana
21:36:37.769 - INFO - [lifespan:1191] -      - Health Check: http://*************:9080/health
21:36:37.769 - INFO - [lifespan:1192] -        基础健康检查
21:36:37.770 - INFO - [lifespan:1193] -      - Detailed Health: http://*************:9080/api/health/detailed
21:36:37.770 - INFO - [lifespan:1194] -        详细健康状态，包含评分和建议
21:36:37.770 - INFO - [lifespan:1196] - 
  🤖 AI API:
21:36:37.770 - INFO - [lifespan:1197] -      - Chat Completions: POST http://*************:9080/v1/chat/completions
21:36:37.770 - INFO - [lifespan:1198] -        OpenAI兼容的聊天API
21:36:37.771 - INFO - [lifespan:1199] -      - List Models: GET http://*************:9080/v1/models
21:36:37.771 - INFO - [lifespan:1200] -        获取可用模型列表
21:36:37.771 - INFO - [lifespan:1201] -      - Refresh Models: POST http://*************:9080/v1/refresh-models
21:36:37.771 - INFO - [lifespan:1202] -        刷新模型列表
21:36:37.771 - INFO - [lifespan:1204] - 
  📈 Statistics:
21:36:37.771 - INFO - [lifespan:1205] -      - Stats Summary: http://*************:9080/api/stats/summary
21:36:37.771 - INFO - [lifespan:1206] -        24小时统计摘要
21:36:37.771 - INFO - [lifespan:1207] -      - Request Logs: http://*************:9080/api/logs/requests
21:36:37.771 - INFO - [lifespan:1208] -        请求日志API
21:36:37.771 - INFO - [lifespan:1209] -      - Error Logs: http://*************:9080/api/logs/errors
21:36:37.771 - INFO - [lifespan:1210] -        错误日志API
21:36:37.772 - INFO - [lifespan:1211] -      - Alerts: http://*************:9080/api/alerts
21:36:37.772 - INFO - [lifespan:1212] -        系统告警历史
21:36:37.772 - INFO - [lifespan:1214] - 
  🛠️  OpenAI Client Config:
21:36:37.772 - INFO - [lifespan:1215] -      base_url='http://*************:9080/v1'
21:36:37.772 - INFO - [lifespan:1216] -      api_key='sk-any-string-you-like'
21:36:37.772 - INFO - [lifespan:1217] - 
============================================================

21:36:37.772 - INFO - [lifespan:1222] - 已加载 85 个备用模型
21:36:37.772 - INFO - [lifespan:1231] - 服务器启动完成
21:36:37.774 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
21:36:40.667 - INFO - [websocket_endpoint:1278] - ✅ 浏览器WebSocket已连接
21:36:40.671 - INFO - [websocket_endpoint:1313] - 🤝 收到重连握手，浏览器有 0 个待处理请求
21:36:40.682 - INFO - [websocket_endpoint:1278] - ✅ 浏览器WebSocket已连接
21:36:40.685 - INFO - [websocket_endpoint:1313] - 🤝 收到重连握手，浏览器有 0 个待处理请求
21:37:08.048 - INFO - [add_request:794] - REQUEST_MGR: Added request b4e3a79d-4f4c-4237-932f-942ac1e292fc for tracking
21:37:08.048 - INFO - [chat_completions:1481] - API [ID: b4e3a79d-4f4c-4237-932f-942ac1e292fc]: Created persistent request for model type 'chat'.
21:37:08.049 - INFO - [chat_completions:1496] - API [ID: b4e3a79d-4f4c-4237-932f-942ac1e292fc]: Returning text/event-stream response to client.
21:37:08.061 - INFO - [create_lmarena_request_body:1984] - Added empty user message after last user message at index 0 for chat model
21:37:08.062 - INFO - [send_to_browser_task:1548] - TASK [ID: b4e3a79d-4f4c-4237-932f-942ac1e292fc]: Sending payload and 0 file(s) to browser.
21:37:08.062 - INFO - [send_to_browser_task:1553] - TASK [ID: b4e3a79d-4f4c-4237-932f-942ac1e292fc]: Payload sent and marked as sent to browser.
21:37:08.063 - INFO - [stream_generator:1597] - STREAMER [ID: b4e3a79d-4f4c-4237-932f-942ac1e292fc]: Generator started for model type 'chat'.
21:37:11.117 - INFO - [complete_request:841] - REQUEST_MGR: Request b4e3a79d-4f4c-4237-932f-942ac1e292fc completed and removed
21:37:11.134 - INFO - [stream_generator:1909] - GENERATOR [ID: b4e3a79d-4f4c-4237-932f-942ac1e292fc]: Cleaned up response channel.
21:41:37.790 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
21:46:37.805 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
21:51:37.833 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
21:56:37.845 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
22:01:37.856 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
22:28:08.611 - INFO - [lifespan:1173] - 服务器正在启动...
22:28:08.613 - INFO - [lifespan:1178] - 🌐 Server access URLs:
22:28:08.613 - INFO - [lifespan:1179] -   - Local: http://localhost:9080
22:28:08.613 - INFO - [lifespan:1180] -   - Network: http://*************:9080
22:28:08.613 - INFO - [lifespan:1181] - 📱 Use the Network URL to access from your phone on the same WiFi
22:28:08.613 - INFO - [lifespan:1184] - 
📋 Available Endpoints:
22:28:08.613 - INFO - [lifespan:1185] -   🖥️  Monitor Dashboard: http://*************:9080/monitor
22:28:08.614 - INFO - [lifespan:1186] -      实时监控面板，查看系统状态、请求日志、性能指标
22:28:08.614 - INFO - [lifespan:1188] - 
  📊 Metrics & Health:
22:28:08.614 - INFO - [lifespan:1189] -      - Prometheus Metrics: http://*************:9080/metrics
22:28:08.614 - INFO - [lifespan:1190] -        Prometheus格式的性能指标，可接入Grafana
22:28:08.614 - INFO - [lifespan:1191] -      - Health Check: http://*************:9080/health
22:28:08.614 - INFO - [lifespan:1192] -        基础健康检查
22:28:08.614 - INFO - [lifespan:1193] -      - Detailed Health: http://*************:9080/api/health/detailed
22:28:08.614 - INFO - [lifespan:1194] -        详细健康状态，包含评分和建议
22:28:08.615 - INFO - [lifespan:1196] - 
  🤖 AI API:
22:28:08.615 - INFO - [lifespan:1197] -      - Chat Completions: POST http://*************:9080/v1/chat/completions
22:28:08.615 - INFO - [lifespan:1198] -        OpenAI兼容的聊天API
22:28:08.615 - INFO - [lifespan:1199] -      - List Models: GET http://*************:9080/v1/models
22:28:08.615 - INFO - [lifespan:1200] -        获取可用模型列表
22:28:08.615 - INFO - [lifespan:1201] -      - Refresh Models: POST http://*************:9080/v1/refresh-models
22:28:08.615 - INFO - [lifespan:1202] -        刷新模型列表
22:28:08.616 - INFO - [lifespan:1204] - 
  📈 Statistics:
22:28:08.616 - INFO - [lifespan:1205] -      - Stats Summary: http://*************:9080/api/stats/summary
22:28:08.616 - INFO - [lifespan:1206] -        24小时统计摘要
22:28:08.616 - INFO - [lifespan:1207] -      - Request Logs: http://*************:9080/api/logs/requests
22:28:08.616 - INFO - [lifespan:1208] -        请求日志API
22:28:08.616 - INFO - [lifespan:1209] -      - Error Logs: http://*************:9080/api/logs/errors
22:28:08.616 - INFO - [lifespan:1210] -        错误日志API
22:28:08.617 - INFO - [lifespan:1211] -      - Alerts: http://*************:9080/api/alerts
22:28:08.617 - INFO - [lifespan:1212] -        系统告警历史
22:28:08.617 - INFO - [lifespan:1214] - 
  🛠️  OpenAI Client Config:
22:28:08.617 - INFO - [lifespan:1215] -      base_url='http://*************:9080/v1'
22:28:08.617 - INFO - [lifespan:1216] -      api_key='sk-any-string-you-like'
22:28:08.617 - INFO - [lifespan:1217] - 
============================================================

22:28:08.617 - INFO - [lifespan:1222] - 已加载 85 个备用模型
22:28:08.617 - INFO - [lifespan:1231] - 服务器启动完成
22:28:08.618 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
22:28:08.632 - INFO - [websocket_endpoint:1278] - ✅ 浏览器WebSocket已连接
22:28:08.644 - INFO - [websocket_endpoint:1313] - 🤝 收到重连握手，浏览器有 0 个待处理请求
22:28:08.647 - INFO - [websocket_endpoint:1278] - ✅ 浏览器WebSocket已连接
22:28:08.652 - INFO - [websocket_endpoint:1313] - 🤝 收到重连握手，浏览器有 0 个待处理请求
22:28:10.626 - INFO - [websocket_endpoint:1278] - ✅ 浏览器WebSocket已连接
22:28:10.628 - INFO - [websocket_endpoint:1313] - 🤝 收到重连握手，浏览器有 0 个待处理请求
22:28:11.951 - WARNING - [websocket_endpoint:1381] - ❌ 浏览器客户端已断开连接
22:28:11.951 - INFO - [websocket_endpoint:1401] - WebSocket cleaned up. Persistent requests kept alive.
22:28:11.953 - WARNING - [websocket_endpoint:1381] - ❌ 浏览器客户端已断开连接
22:28:11.953 - INFO - [websocket_endpoint:1401] - WebSocket cleaned up. Persistent requests kept alive.
22:28:12.869 - INFO - [websocket_endpoint:1278] - ✅ 浏览器WebSocket已连接
22:28:12.875 - INFO - [websocket_endpoint:1313] - 🤝 收到重连握手，浏览器有 0 个待处理请求
22:28:38.635 - ERROR - [start_heartbeat:543] - 心跳发送失败: Unexpected ASGI message 'websocket.send', after sending 'websocket.close' or response already completed.
22:28:38.667 - ERROR - [start_heartbeat:543] - 心跳发送失败: Unexpected ASGI message 'websocket.send', after sending 'websocket.close' or response already completed.
22:31:47.105 - INFO - [add_request:794] - REQUEST_MGR: Added request b314838d-53d0-4927-b791-8f15347f380b for tracking
22:31:47.106 - INFO - [chat_completions:1481] - API [ID: b314838d-53d0-4927-b791-8f15347f380b]: Created persistent request for model type 'chat'.
22:31:47.106 - INFO - [chat_completions:1496] - API [ID: b314838d-53d0-4927-b791-8f15347f380b]: Returning text/event-stream response to client.
22:31:47.114 - INFO - [create_lmarena_request_body:1984] - Added empty user message after last user message at index 0 for chat model
22:31:47.115 - INFO - [send_to_browser_task:1548] - TASK [ID: b314838d-53d0-4927-b791-8f15347f380b]: Sending payload and 0 file(s) to browser.
22:31:47.115 - INFO - [send_to_browser_task:1553] - TASK [ID: b314838d-53d0-4927-b791-8f15347f380b]: Payload sent and marked as sent to browser.
22:31:47.117 - INFO - [stream_generator:1597] - STREAMER [ID: b314838d-53d0-4927-b791-8f15347f380b]: Generator started for model type 'chat'.
22:31:49.915 - INFO - [complete_request:841] - REQUEST_MGR: Request b314838d-53d0-4927-b791-8f15347f380b completed and removed
22:31:49.936 - INFO - [stream_generator:1909] - GENERATOR [ID: b314838d-53d0-4927-b791-8f15347f380b]: Cleaned up response channel.
22:31:54.240 - INFO - [add_request:794] - REQUEST_MGR: Added request 3adccf0c-4f6f-4158-b481-b53aae745635 for tracking
22:31:54.240 - INFO - [chat_completions:1481] - API [ID: 3adccf0c-4f6f-4158-b481-b53aae745635]: Created persistent request for model type 'chat'.
22:31:54.240 - INFO - [chat_completions:1496] - API [ID: 3adccf0c-4f6f-4158-b481-b53aae745635]: Returning text/event-stream response to client.
22:31:54.241 - INFO - [create_lmarena_request_body:1984] - Added empty user message after last user message at index 0 for chat model
22:31:54.241 - INFO - [send_to_browser_task:1548] - TASK [ID: 3adccf0c-4f6f-4158-b481-b53aae745635]: Sending payload and 0 file(s) to browser.
22:31:54.241 - INFO - [send_to_browser_task:1553] - TASK [ID: 3adccf0c-4f6f-4158-b481-b53aae745635]: Payload sent and marked as sent to browser.
22:31:54.242 - INFO - [stream_generator:1597] - STREAMER [ID: 3adccf0c-4f6f-4158-b481-b53aae745635]: Generator started for model type 'chat'.
22:31:59.462 - INFO - [complete_request:841] - REQUEST_MGR: Request 3adccf0c-4f6f-4158-b481-b53aae745635 completed and removed
22:31:59.466 - INFO - [stream_generator:1909] - GENERATOR [ID: 3adccf0c-4f6f-4158-b481-b53aae745635]: Cleaned up response channel.
22:32:13.572 - INFO - [add_request:794] - REQUEST_MGR: Added request b649e5ec-9fc8-4818-a8fa-d98a754568a4 for tracking
22:32:13.573 - INFO - [chat_completions:1481] - API [ID: b649e5ec-9fc8-4818-a8fa-d98a754568a4]: Created persistent request for model type 'chat'.
22:32:13.573 - INFO - [chat_completions:1496] - API [ID: b649e5ec-9fc8-4818-a8fa-d98a754568a4]: Returning text/event-stream response to client.
22:32:13.573 - INFO - [create_lmarena_request_body:1984] - Added empty user message after last user message at index 0 for chat model
22:32:13.573 - INFO - [send_to_browser_task:1548] - TASK [ID: b649e5ec-9fc8-4818-a8fa-d98a754568a4]: Sending payload and 0 file(s) to browser.
22:32:13.574 - INFO - [send_to_browser_task:1553] - TASK [ID: b649e5ec-9fc8-4818-a8fa-d98a754568a4]: Payload sent and marked as sent to browser.
22:32:13.574 - INFO - [stream_generator:1597] - STREAMER [ID: b649e5ec-9fc8-4818-a8fa-d98a754568a4]: Generator started for model type 'chat'.
22:32:13.883 - INFO - [add_request:794] - REQUEST_MGR: Added request 32ee62d5-3ec6-45c3-8d02-fc269196c476 for tracking
22:32:13.883 - INFO - [chat_completions:1481] - API [ID: 32ee62d5-3ec6-45c3-8d02-fc269196c476]: Created persistent request for model type 'chat'.
22:32:13.883 - INFO - [chat_completions:1496] - API [ID: 32ee62d5-3ec6-45c3-8d02-fc269196c476]: Returning text/event-stream response to client.
22:32:13.883 - INFO - [create_lmarena_request_body:1984] - Added empty user message after last user message at index 0 for chat model
22:32:13.884 - INFO - [send_to_browser_task:1548] - TASK [ID: 32ee62d5-3ec6-45c3-8d02-fc269196c476]: Sending payload and 0 file(s) to browser.
22:32:13.884 - INFO - [send_to_browser_task:1553] - TASK [ID: 32ee62d5-3ec6-45c3-8d02-fc269196c476]: Payload sent and marked as sent to browser.
22:32:13.885 - INFO - [stream_generator:1597] - STREAMER [ID: 32ee62d5-3ec6-45c3-8d02-fc269196c476]: Generator started for model type 'chat'.
22:32:16.926 - INFO - [complete_request:841] - REQUEST_MGR: Request b649e5ec-9fc8-4818-a8fa-d98a754568a4 completed and removed
22:32:17.002 - INFO - [stream_generator:1909] - GENERATOR [ID: b649e5ec-9fc8-4818-a8fa-d98a754568a4]: Cleaned up response channel.
22:32:17.191 - INFO - [complete_request:841] - REQUEST_MGR: Request 32ee62d5-3ec6-45c3-8d02-fc269196c476 completed and removed
22:32:17.203 - INFO - [stream_generator:1909] - GENERATOR [ID: 32ee62d5-3ec6-45c3-8d02-fc269196c476]: Cleaned up response channel.
22:33:08.638 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
22:38:08.639 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
22:43:08.652 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
22:45:40.261 - INFO - [lifespan:1191] - 服务器正在启动...
22:45:40.262 - INFO - [lifespan:1221] - 🌐 Server access URLs:
22:45:40.264 - INFO - [lifespan:1222] -   - Local: http://localhost:9080
22:45:40.264 - INFO - [lifespan:1223] -   - Network: http://*************:9080
22:45:40.264 - INFO - [lifespan:1226] - 📱 Use the Network URL to access from your phone on the same WiFi
22:45:40.264 - INFO - [lifespan:1229] - 
📋 Available Endpoints:
22:45:40.265 - INFO - [lifespan:1230] -   🖥️  Monitor Dashboard: http://*************:9080/monitor
22:45:40.265 - INFO - [lifespan:1231] -      实时监控面板，查看系统状态、请求日志、性能指标
22:45:40.265 - INFO - [lifespan:1233] - 
  📊 Metrics & Health:
22:45:40.265 - INFO - [lifespan:1234] -      - Prometheus Metrics: http://*************:9080/metrics
22:45:40.266 - INFO - [lifespan:1235] -        Prometheus格式的性能指标，可接入Grafana
22:45:40.266 - INFO - [lifespan:1236] -      - Health Check: http://*************:9080/health
22:45:40.266 - INFO - [lifespan:1237] -        基础健康检查
22:45:40.266 - INFO - [lifespan:1238] -      - Detailed Health: http://*************:9080/api/health/detailed
22:45:40.266 - INFO - [lifespan:1239] -        详细健康状态，包含评分和建议
22:45:40.266 - INFO - [lifespan:1241] - 
  🤖 AI API:
22:45:40.266 - INFO - [lifespan:1242] -      - Chat Completions: POST http://*************:9080/v1/chat/completions
22:45:40.267 - INFO - [lifespan:1243] -        OpenAI兼容的聊天API
22:45:40.267 - INFO - [lifespan:1244] -      - List Models: GET http://*************:9080/v1/models
22:45:40.267 - INFO - [lifespan:1245] -        获取可用模型列表
22:45:40.267 - INFO - [lifespan:1246] -      - Refresh Models: POST http://*************:9080/v1/refresh-models
22:45:40.267 - INFO - [lifespan:1247] -        刷新模型列表
22:45:40.267 - INFO - [lifespan:1249] - 
  📈 Statistics:
22:45:40.268 - INFO - [lifespan:1250] -      - Stats Summary: http://*************:9080/api/stats/summary
22:45:40.268 - INFO - [lifespan:1251] -        24小时统计摘要
22:45:40.268 - INFO - [lifespan:1252] -      - Request Logs: http://*************:9080/api/logs/requests
22:45:40.268 - INFO - [lifespan:1253] -        请求日志API
22:45:40.270 - INFO - [lifespan:1254] -      - Error Logs: http://*************:9080/api/logs/errors
22:45:40.270 - INFO - [lifespan:1255] -        错误日志API
22:45:40.270 - INFO - [lifespan:1256] -      - Alerts: http://*************:9080/api/alerts
22:45:40.271 - INFO - [lifespan:1257] -        系统告警历史
22:45:40.271 - INFO - [lifespan:1259] - 
  🛠️  OpenAI Client Config:
22:45:40.271 - INFO - [lifespan:1260] -      base_url='http://*************:9080/v1'
22:45:40.271 - INFO - [lifespan:1261] -      api_key='sk-any-string-you-like'
22:45:40.271 - INFO - [lifespan:1262] - 
============================================================

22:45:40.272 - INFO - [lifespan:1267] - 已加载 85 个备用模型
22:45:40.272 - INFO - [lifespan:1276] - 服务器启动完成
22:45:40.273 - INFO - [periodic_cleanup:726] - 清理任务执行完成. 活跃请求: 0
22:45:40.274 - INFO - [lifespan:1283] - 生命周期: 服务器正在关闭。正在取消 2 个后台任务...
22:45:40.275 - INFO - [lifespan:1287] - 正在停止内网穿透...
22:45:40.308 - ERROR - [stop_tunnel:296] - 停止内网穿透失败: module 'pyngrok.ngrok' has no attribute 'disconnect_all'
22:45:40.309 - INFO - [lifespan:1294] - 生命周期: 正在取消任务: <Task pending name='Task-3' coro=<periodic_cleanup() running at D:\aa1\lmarena-proxy-main\proxy_server.py:731> wait_for=<Future pending cb=[Task.task_wakeup()]>>
22:45:40.309 - INFO - [lifespan:1294] - 生命周期: 正在取消任务: <Task pending name='Task-4' coro=<MonitoringAlerts.check_system_health() running at D:\aa1\lmarena-proxy-main\proxy_server.py:596> wait_for=<Future pending cb=[Task.task_wakeup()]>>
22:45:40.309 - INFO - [lifespan:1300] - 生命周期: 等待 2 个已取消的任务完成...
22:45:40.310 - INFO - [lifespan:1306] - 生命周期: 任务 0 正常完成
22:45:40.310 - INFO - [lifespan:1306] - 生命周期: 任务 1 正常完成
22:45:40.310 - INFO - [lifespan:1308] - 生命周期: 所有后台任务已取消。关闭完成。
22:46:34.774 - INFO - [lifespan:1191] - 服务器正在启动...
22:46:34.775 - INFO - [lifespan:1221] - 🌐 Server access URLs:
22:46:34.776 - INFO - [lifespan:1222] -   - Local: http://localhost:9081
22:46:34.776 - INFO - [lifespan:1223] -   - Network: http://*************:9081
22:46:34.777 - INFO - [lifespan:1226] - 📱 Use the Network URL to access from your phone on the same WiFi
22:46:34.777 - INFO - [lifespan:1229] - 
📋 Available Endpoints:
22:46:34.777 - INFO - [lifespan:1230] -   🖥️  Monitor Dashboard: http://*************:9081/monitor
22:46:34.777 - INFO - [lifespan:1231] -      实时监控面板，查看系统状态、请求日志、性能指标
22:46:34.778 - INFO - [lifespan:1233] - 
  📊 Metrics & Health:
22:46:34.778 - INFO - [lifespan:1234] -      - Prometheus Metrics: http://*************:9081/metrics
22:46:34.778 - INFO - [lifespan:1235] -        Prometheus格式的性能指标，可接入Grafana
22:46:34.779 - INFO - [lifespan:1236] -      - Health Check: http://*************:9081/health
22:46:34.779 - INFO - [lifespan:1237] -        基础健康检查
22:46:34.779 - INFO - [lifespan:1238] -      - Detailed Health: http://*************:9081/api/health/detailed
22:46:34.779 - INFO - [lifespan:1239] -        详细健康状态，包含评分和建议
22:46:34.780 - INFO - [lifespan:1241] - 
  🤖 AI API:
22:46:34.780 - INFO - [lifespan:1242] -      - Chat Completions: POST http://*************:9081/v1/chat/completions
22:46:34.780 - INFO - [lifespan:1243] -        OpenAI兼容的聊天API
22:46:34.780 - INFO - [lifespan:1244] -      - List Models: GET http://*************:9081/v1/models
22:46:34.780 - INFO - [lifespan:1245] -        获取可用模型列表
22:46:34.781 - INFO - [lifespan:1246] -      - Refresh Models: POST http://*************:9081/v1/refresh-models
22:46:34.781 - INFO - [lifespan:1247] -        刷新模型列表
22:46:34.781 - INFO - [lifespan:1249] - 
  📈 Statistics:
22:46:34.781 - INFO - [lifespan:1250] -      - Stats Summary: http://*************:9081/api/stats/summary
22:46:34.781 - INFO - [lifespan:1251] -        24小时统计摘要
22:46:34.782 - INFO - [lifespan:1252] -      - Request Logs: http://*************:9081/api/logs/requests
22:46:34.782 - INFO - [lifespan:1253] -        请求日志API
22:46:34.782 - INFO - [lifespan:1254] -      - Error Logs: http://*************:9081/api/logs/errors
22:46:34.782 - INFO - [lifespan:1255] -        错误日志API
22:46:34.783 - INFO - [lifespan:1256] -      - Alerts: http://*************:9081/api/alerts
22:46:34.783 - INFO - [lifespan:1257] -        系统告警历史
22:46:34.783 - INFO - [lifespan:1259] - 
  🛠️  OpenAI Client Config:
22:46:34.783 - INFO - [lifespan:1260] -      base_url='http://*************:9081/v1'
22:46:34.783 - INFO - [lifespan:1261] -      api_key='sk-any-string-you-like'
22:46:34.784 - INFO - [lifespan:1262] - 
============================================================

22:46:34.784 - INFO - [lifespan:1267] - 已加载 85 个备用模型
22:46:34.785 - INFO - [lifespan:1276] - 服务器启动完成
22:46:34.785 - INFO - [periodic_cleanup:726] - 清理任务执行完成. 活跃请求: 0
22:48:08.667 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
22:51:34.804 - INFO - [periodic_cleanup:726] - 清理任务执行完成. 活跃请求: 0
22:52:04.833 - WARNING - [send_alert:678] - 系统告警: 浏览器已断线 5 分钟
22:52:06.255 - INFO - [websocket_endpoint:1278] - ✅ 浏览器WebSocket已连接
22:52:06.271 - INFO - [websocket_endpoint:1278] - ✅ 浏览器WebSocket已连接
22:52:06.582 - INFO - [websocket_endpoint:1313] - 🤝 收到重连握手，浏览器有 0 个待处理请求
22:52:06.583 - INFO - [websocket_endpoint:1313] - 🤝 收到重连握手，浏览器有 0 个待处理请求
22:52:08.976 - WARNING - [websocket_endpoint:1381] - ❌ 浏览器客户端已断开连接
22:52:08.977 - INFO - [websocket_endpoint:1401] - WebSocket cleaned up. Persistent requests kept alive.
22:52:08.978 - WARNING - [websocket_endpoint:1381] - ❌ 浏览器客户端已断开连接
22:52:08.978 - INFO - [websocket_endpoint:1401] - WebSocket cleaned up. Persistent requests kept alive.
22:52:34.846 - WARNING - [send_alert:678] - 系统告警: 浏览器已断线 5 分钟
22:52:36.270 - ERROR - [start_heartbeat:543] - 心跳发送失败: Unexpected ASGI message 'websocket.send', after sending 'websocket.close' or response already completed.
22:52:36.270 - ERROR - [start_heartbeat:543] - 心跳发送失败: Unexpected ASGI message 'websocket.send', after sending 'websocket.close' or response already completed.
22:53:04.865 - WARNING - [send_alert:678] - 系统告警: 浏览器已断线 6 分钟
22:53:08.694 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
22:53:34.872 - WARNING - [send_alert:678] - 系统告警: 浏览器已断线 6 分钟
22:55:26.098 - INFO - [lifespan:1199] - 服务器正在启动...
22:55:26.099 - INFO - [lifespan:1229] - 🌐 Server access URLs:
22:55:26.099 - INFO - [lifespan:1230] -   - Local: http://localhost:9081
22:55:26.100 - INFO - [lifespan:1231] -   - Network: http://*************:9081
22:55:26.100 - INFO - [lifespan:1234] - 📱 Use the Network URL to access from your phone on the same WiFi
22:55:26.100 - INFO - [lifespan:1237] - 
📋 Available Endpoints:
22:55:26.100 - INFO - [lifespan:1238] -   🖥️  Monitor Dashboard: http://*************:9081/monitor
22:55:26.100 - INFO - [lifespan:1239] -      实时监控面板，查看系统状态、请求日志、性能指标
22:55:26.101 - INFO - [lifespan:1241] - 
  📊 Metrics & Health:
22:55:26.101 - INFO - [lifespan:1242] -      - Prometheus Metrics: http://*************:9081/metrics
22:55:26.101 - INFO - [lifespan:1243] -        Prometheus格式的性能指标，可接入Grafana
22:55:26.101 - INFO - [lifespan:1244] -      - Health Check: http://*************:9081/health
22:55:26.101 - INFO - [lifespan:1245] -        基础健康检查
22:55:26.101 - INFO - [lifespan:1246] -      - Detailed Health: http://*************:9081/api/health/detailed
22:55:26.102 - INFO - [lifespan:1247] -        详细健康状态，包含评分和建议
22:55:26.102 - INFO - [lifespan:1249] - 
  🤖 AI API:
22:55:26.102 - INFO - [lifespan:1250] -      - Chat Completions: POST http://*************:9081/v1/chat/completions
22:55:26.102 - INFO - [lifespan:1251] -        OpenAI兼容的聊天API
22:55:26.104 - INFO - [lifespan:1252] -      - List Models: GET http://*************:9081/v1/models
22:55:26.104 - INFO - [lifespan:1253] -        获取可用模型列表
22:55:26.105 - INFO - [lifespan:1254] -      - Refresh Models: POST http://*************:9081/v1/refresh-models
22:55:26.105 - INFO - [lifespan:1255] -        刷新模型列表
22:55:26.105 - INFO - [lifespan:1257] - 
  📈 Statistics:
22:55:26.105 - INFO - [lifespan:1258] -      - Stats Summary: http://*************:9081/api/stats/summary
22:55:26.105 - INFO - [lifespan:1259] -        24小时统计摘要
22:55:26.106 - INFO - [lifespan:1260] -      - Request Logs: http://*************:9081/api/logs/requests
22:55:26.106 - INFO - [lifespan:1261] -        请求日志API
22:55:26.106 - INFO - [lifespan:1262] -      - Error Logs: http://*************:9081/api/logs/errors
22:55:26.106 - INFO - [lifespan:1263] -        错误日志API
22:55:26.107 - INFO - [lifespan:1264] -      - Alerts: http://*************:9081/api/alerts
22:55:26.107 - INFO - [lifespan:1265] -        系统告警历史
22:55:26.107 - INFO - [lifespan:1267] - 
  🛠️  OpenAI Client Config:
22:55:26.107 - INFO - [lifespan:1268] -      base_url='http://*************:9081/v1'
22:55:26.107 - INFO - [lifespan:1269] -      api_key='sk-any-string-you-like'
22:55:26.108 - INFO - [lifespan:1270] - 
============================================================

22:55:26.108 - INFO - [lifespan:1275] - 已加载 85 个备用模型
22:55:26.108 - INFO - [lifespan:1284] - 服务器启动完成
22:55:26.108 - INFO - [periodic_cleanup:734] - 清理任务执行完成. 活跃请求: 0
22:57:39.091 - WARNING - [send_alert:660] - 系统告警: 浏览器已断线 5 分钟
22:58:08.717 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
22:58:09.075 - WARNING - [send_alert:660] - 系统告警: 浏览器已断线 5 分钟
22:58:39.095 - WARNING - [send_alert:660] - 系统告警: 浏览器已断线 6 分钟
22:58:56.240 - WARNING - [websocket_endpoint:1381] - ❌ 浏览器客户端已断开连接
22:58:56.240 - INFO - [websocket_endpoint:1401] - WebSocket cleaned up. Persistent requests kept alive.
22:58:56.241 - WARNING - [websocket_endpoint:1381] - ❌ 浏览器客户端已断开连接
22:58:56.241 - INFO - [websocket_endpoint:1401] - WebSocket cleaned up. Persistent requests kept alive.
22:58:56.791 - INFO - [websocket_endpoint:1278] - ✅ 浏览器WebSocket已连接
22:58:56.806 - INFO - [websocket_endpoint:1278] - ✅ 浏览器WebSocket已连接
22:58:56.878 - INFO - [websocket_endpoint:1313] - 🤝 收到重连握手，浏览器有 0 个待处理请求
22:58:57.076 - INFO - [websocket_endpoint:1313] - 🤝 收到重连握手，浏览器有 0 个待处理请求
22:59:11.067 - ERROR - [start_heartbeat:543] - 心跳发送失败: Unexpected ASGI message 'websocket.send', after sending 'websocket.close' or response already completed.
22:59:13.469 - ERROR - [start_heartbeat:543] - 心跳发送失败: Unexpected ASGI message 'websocket.send', after sending 'websocket.close' or response already completed.
23:00:26.108 - INFO - [periodic_cleanup:734] - 清理任务执行完成. 活跃请求: 0
23:00:56.180 - WARNING - [send_alert:686] - 系统告警: 浏览器已断线 5 分钟
23:01:03.555 - INFO - [add_request:794] - REQUEST_MGR: Added request 214ba9a2-2a73-4519-a88a-151a63d11288 for tracking
23:01:03.556 - INFO - [chat_completions:1481] - API [ID: 214ba9a2-2a73-4519-a88a-151a63d11288]: Created persistent request for model type 'chat'.
23:01:03.556 - INFO - [chat_completions:1496] - API [ID: 214ba9a2-2a73-4519-a88a-151a63d11288]: Returning text/event-stream response to client.
23:01:03.559 - INFO - [create_lmarena_request_body:1984] - Added empty user message after last user message at index 6 for chat model
23:01:03.560 - INFO - [send_to_browser_task:1548] - TASK [ID: 214ba9a2-2a73-4519-a88a-151a63d11288]: Sending payload and 0 file(s) to browser.
23:01:03.561 - INFO - [send_to_browser_task:1553] - TASK [ID: 214ba9a2-2a73-4519-a88a-151a63d11288]: Payload sent and marked as sent to browser.
23:01:03.561 - INFO - [stream_generator:1597] - STREAMER [ID: 214ba9a2-2a73-4519-a88a-151a63d11288]: Generator started for model type 'chat'.
23:01:26.199 - WARNING - [send_alert:686] - 系统告警: 浏览器已断线 5 分钟
23:01:30.498 - INFO - [complete_request:841] - REQUEST_MGR: Request 214ba9a2-2a73-4519-a88a-151a63d11288 completed and removed
23:01:30.501 - INFO - [stream_generator:1909] - GENERATOR [ID: 214ba9a2-2a73-4519-a88a-151a63d11288]: Cleaned up response channel.
23:01:56.221 - WARNING - [send_alert:686] - 系统告警: 浏览器已断线 6 分钟
23:03:08.727 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
23:08:08.729 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
23:13:08.729 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
23:14:03.846 - INFO - [add_request:794] - REQUEST_MGR: Added request 24dda259-a274-4b73-a28b-1ab43bb7ecc6 for tracking
23:14:03.846 - INFO - [chat_completions:1481] - API [ID: 24dda259-a274-4b73-a28b-1ab43bb7ecc6]: Created persistent request for model type 'chat'.
23:14:03.847 - INFO - [chat_completions:1496] - API [ID: 24dda259-a274-4b73-a28b-1ab43bb7ecc6]: Returning text/event-stream response to client.
23:14:03.847 - INFO - [create_lmarena_request_body:1984] - Added empty user message after last user message at index 0 for chat model
23:14:03.847 - INFO - [send_to_browser_task:1548] - TASK [ID: 24dda259-a274-4b73-a28b-1ab43bb7ecc6]: Sending payload and 0 file(s) to browser.
23:14:03.849 - INFO - [send_to_browser_task:1553] - TASK [ID: 24dda259-a274-4b73-a28b-1ab43bb7ecc6]: Payload sent and marked as sent to browser.
23:14:03.849 - INFO - [stream_generator:1597] - STREAMER [ID: 24dda259-a274-4b73-a28b-1ab43bb7ecc6]: Generator started for model type 'chat'.
23:14:04.162 - INFO - [add_request:794] - REQUEST_MGR: Added request 2b79af3c-afed-426e-91b6-2b4895f81d55 for tracking
23:14:04.162 - INFO - [chat_completions:1481] - API [ID: 2b79af3c-afed-426e-91b6-2b4895f81d55]: Created persistent request for model type 'chat'.
23:14:04.162 - INFO - [chat_completions:1496] - API [ID: 2b79af3c-afed-426e-91b6-2b4895f81d55]: Returning text/event-stream response to client.
23:14:04.163 - INFO - [create_lmarena_request_body:1984] - Added empty user message after last user message at index 0 for chat model
23:14:04.163 - INFO - [send_to_browser_task:1548] - TASK [ID: 2b79af3c-afed-426e-91b6-2b4895f81d55]: Sending payload and 0 file(s) to browser.
23:14:04.163 - INFO - [send_to_browser_task:1553] - TASK [ID: 2b79af3c-afed-426e-91b6-2b4895f81d55]: Payload sent and marked as sent to browser.
23:14:04.164 - INFO - [stream_generator:1597] - STREAMER [ID: 2b79af3c-afed-426e-91b6-2b4895f81d55]: Generator started for model type 'chat'.
23:14:06.690 - INFO - [complete_request:841] - REQUEST_MGR: Request 24dda259-a274-4b73-a28b-1ab43bb7ecc6 completed and removed
23:14:06.739 - INFO - [stream_generator:1909] - GENERATOR [ID: 24dda259-a274-4b73-a28b-1ab43bb7ecc6]: Cleaned up response channel.
23:14:06.883 - INFO - [complete_request:841] - REQUEST_MGR: Request 2b79af3c-afed-426e-91b6-2b4895f81d55 completed and removed
23:14:06.893 - INFO - [stream_generator:1909] - GENERATOR [ID: 2b79af3c-afed-426e-91b6-2b4895f81d55]: Cleaned up response channel.
23:18:08.740 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
23:23:08.744 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
23:28:08.751 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
23:33:08.750 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
23:36:02.178 - WARNING - [websocket_endpoint:1381] - ❌ 浏览器客户端已断开连接
23:36:02.178 - INFO - [websocket_endpoint:1401] - WebSocket cleaned up. Persistent requests kept alive.
23:36:02.179 - WARNING - [websocket_endpoint:1381] - ❌ 浏览器客户端已断开连接
23:36:02.179 - INFO - [websocket_endpoint:1401] - WebSocket cleaned up. Persistent requests kept alive.
09:32:20.225 - INFO - [lifespan:1200] - 服务器正在启动...
09:32:20.227 - INFO - [lifespan:1230] - 🌐 Server access URLs:
09:32:20.227 - INFO - [lifespan:1231] -   - Local: http://localhost:9080
09:32:20.227 - INFO - [lifespan:1232] -   - Network: http://*************:9080
09:32:20.227 - INFO - [lifespan:1235] - 📱 Use the Network URL to access from your phone on the same WiFi
09:32:20.227 - INFO - [lifespan:1238] - 
📋 Available Endpoints:
09:32:20.229 - INFO - [lifespan:1239] -   🖥️  Monitor Dashboard: http://*************:9080/monitor
09:32:20.229 - INFO - [lifespan:1240] -      实时监控面板，查看系统状态、请求日志、性能指标
09:32:20.229 - INFO - [lifespan:1242] - 
  📊 Metrics & Health:
09:32:20.229 - INFO - [lifespan:1243] -      - Prometheus Metrics: http://*************:9080/metrics
09:32:20.230 - INFO - [lifespan:1244] -        Prometheus格式的性能指标，可接入Grafana
09:32:20.230 - INFO - [lifespan:1245] -      - Health Check: http://*************:9080/health
09:32:20.230 - INFO - [lifespan:1246] -        基础健康检查
09:32:20.230 - INFO - [lifespan:1247] -      - Detailed Health: http://*************:9080/api/health/detailed
09:32:20.230 - INFO - [lifespan:1248] -        详细健康状态，包含评分和建议
09:32:20.231 - INFO - [lifespan:1250] - 
  🤖 AI API:
09:32:20.231 - INFO - [lifespan:1251] -      - Chat Completions: POST http://*************:9080/v1/chat/completions
09:32:20.231 - INFO - [lifespan:1252] -        OpenAI兼容的聊天API
09:32:20.231 - INFO - [lifespan:1253] -      - List Models: GET http://*************:9080/v1/models
09:32:20.231 - INFO - [lifespan:1254] -        获取可用模型列表
09:32:20.232 - INFO - [lifespan:1255] -      - Refresh Models: POST http://*************:9080/v1/refresh-models
09:32:20.232 - INFO - [lifespan:1256] -        刷新模型列表
09:32:20.232 - INFO - [lifespan:1258] - 
  📈 Statistics:
09:32:20.232 - INFO - [lifespan:1259] -      - Stats Summary: http://*************:9080/api/stats/summary
09:32:20.232 - INFO - [lifespan:1260] -        24小时统计摘要
09:32:20.232 - INFO - [lifespan:1261] -      - Request Logs: http://*************:9080/api/logs/requests
09:32:20.232 - INFO - [lifespan:1262] -        请求日志API
09:32:20.233 - INFO - [lifespan:1263] -      - Error Logs: http://*************:9080/api/logs/errors
09:32:20.233 - INFO - [lifespan:1264] -        错误日志API
09:32:20.233 - INFO - [lifespan:1265] -      - Alerts: http://*************:9080/api/alerts
09:32:20.233 - INFO - [lifespan:1266] -        系统告警历史
09:32:20.234 - INFO - [lifespan:1268] - 
  🛠️  OpenAI Client Config:
09:32:20.234 - INFO - [lifespan:1269] -      base_url='http://*************:9080/v1'
09:32:20.234 - INFO - [lifespan:1270] -      api_key='sk-any-string-you-like'
09:32:20.234 - INFO - [lifespan:1271] - 
============================================================

09:32:20.234 - INFO - [lifespan:1276] - 已加载 85 个备用模型
09:32:20.235 - INFO - [lifespan:1285] - 服务器启动完成
09:32:20.237 - INFO - [periodic_cleanup:735] - 清理任务执行完成. 活跃请求: 0
09:32:20.727 - INFO - [websocket_endpoint:1337] - ✅ 浏览器WebSocket已连接
09:32:20.731 - INFO - [websocket_endpoint:1372] - 🤝 收到重连握手，浏览器有 0 个待处理请求
09:32:20.744 - INFO - [websocket_endpoint:1337] - ✅ 浏览器WebSocket已连接
09:32:20.747 - INFO - [websocket_endpoint:1372] - 🤝 收到重连握手，浏览器有 0 个待处理请求
09:32:45.034 - INFO - [add_request:821] - REQUEST_MGR: Added request a7168ebb-2d48-4098-9fd3-ab6926078979 for tracking
09:32:45.035 - INFO - [chat_completions:1540] - API [ID: a7168ebb-2d48-4098-9fd3-ab6926078979]: Created persistent request for model type 'chat'.
09:32:45.035 - INFO - [chat_completions:1555] - API [ID: a7168ebb-2d48-4098-9fd3-ab6926078979]: Returning text/event-stream response to client.
09:32:45.044 - INFO - [create_lmarena_request_body:2043] - Added empty user message after last user message at index 6 for chat model
09:32:45.044 - INFO - [send_to_browser_task:1607] - TASK [ID: a7168ebb-2d48-4098-9fd3-ab6926078979]: Sending payload and 1 file(s) to browser.
09:32:45.049 - INFO - [send_to_browser_task:1612] - TASK [ID: a7168ebb-2d48-4098-9fd3-ab6926078979]: Payload sent and marked as sent to browser.
09:32:45.049 - INFO - [stream_generator:1656] - STREAMER [ID: a7168ebb-2d48-4098-9fd3-ab6926078979]: Generator started for model type 'chat'.
09:32:45.310 - INFO - [complete_request:868] - REQUEST_MGR: Request a7168ebb-2d48-4098-9fd3-ab6926078979 completed and removed
09:32:45.310 - ERROR - [stream_generator:1729] - STREAMER [ID: a7168ebb-2d48-4098-9fd3-ab6926078979]: Server returned error: {'error': 'File upload failed: Could not parse signed URL response. Response: Server action not found.'}
09:32:45.313 - INFO - [stream_generator:1968] - GENERATOR [ID: a7168ebb-2d48-4098-9fd3-ab6926078979]: Cleaned up response channel.
09:33:03.105 - INFO - [add_request:821] - REQUEST_MGR: Added request f75e04b5-134d-4af2-88d4-687c7cdfe225 for tracking
09:33:03.105 - INFO - [chat_completions:1540] - API [ID: f75e04b5-134d-4af2-88d4-687c7cdfe225]: Created persistent request for model type 'chat'.
09:33:03.106 - INFO - [chat_completions:1555] - API [ID: f75e04b5-134d-4af2-88d4-687c7cdfe225]: Returning text/event-stream response to client.
09:33:03.106 - INFO - [create_lmarena_request_body:2043] - Added empty user message after last user message at index 6 for chat model
09:33:03.106 - INFO - [send_to_browser_task:1607] - TASK [ID: f75e04b5-134d-4af2-88d4-687c7cdfe225]: Sending payload and 1 file(s) to browser.
09:33:03.112 - INFO - [send_to_browser_task:1612] - TASK [ID: f75e04b5-134d-4af2-88d4-687c7cdfe225]: Payload sent and marked as sent to browser.
09:33:03.113 - INFO - [stream_generator:1656] - STREAMER [ID: f75e04b5-134d-4af2-88d4-687c7cdfe225]: Generator started for model type 'chat'.
09:33:03.380 - INFO - [complete_request:868] - REQUEST_MGR: Request f75e04b5-134d-4af2-88d4-687c7cdfe225 completed and removed
09:33:03.381 - ERROR - [stream_generator:1729] - STREAMER [ID: f75e04b5-134d-4af2-88d4-687c7cdfe225]: Server returned error: {'error': 'File upload failed: Could not parse signed URL response. Response: Server action not found.'}
09:33:03.381 - INFO - [stream_generator:1968] - GENERATOR [ID: f75e04b5-134d-4af2-88d4-687c7cdfe225]: Cleaned up response channel.
09:33:15.835 - WARNING - [websocket_endpoint:1440] - ❌ 浏览器客户端已断开连接
09:33:15.836 - INFO - [websocket_endpoint:1460] - WebSocket cleaned up. Persistent requests kept alive.
09:33:15.837 - WARNING - [websocket_endpoint:1440] - ❌ 浏览器客户端已断开连接
09:33:15.837 - INFO - [websocket_endpoint:1460] - WebSocket cleaned up. Persistent requests kept alive.
09:33:16.272 - INFO - [websocket_endpoint:1337] - ✅ 浏览器WebSocket已连接
09:33:16.288 - INFO - [websocket_endpoint:1337] - ✅ 浏览器WebSocket已连接
09:33:16.540 - INFO - [websocket_endpoint:1372] - 🤝 收到重连握手，浏览器有 0 个待处理请求
09:33:16.541 - INFO - [websocket_endpoint:1372] - 🤝 收到重连握手，浏览器有 0 个待处理请求
09:33:20.730 - ERROR - [start_heartbeat:570] - 心跳发送失败: Unexpected ASGI message 'websocket.send', after sending 'websocket.close' or response already completed.
09:33:20.760 - ERROR - [start_heartbeat:570] - 心跳发送失败: Unexpected ASGI message 'websocket.send', after sending 'websocket.close' or response already completed.
09:37:20.234 - WARNING - [cleanup_old_requests:715] - 清理超时请求: a7168ebb-2d48-4098-9fd3-ab6926078979
09:37:20.235 - WARNING - [cleanup_old_requests:715] - 清理超时请求: f75e04b5-134d-4af2-88d4-687c7cdfe225
09:37:20.236 - INFO - [periodic_cleanup:735] - 清理任务执行完成. 活跃请求: 0
09:42:20.229 - INFO - [periodic_cleanup:735] - 清理任务执行完成. 活跃请求: 0
09:47:20.238 - INFO - [periodic_cleanup:735] - 清理任务执行完成. 活跃请求: 0
09:52:20.249 - INFO - [periodic_cleanup:735] - 清理任务执行完成. 活跃请求: 0
