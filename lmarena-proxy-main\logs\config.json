{"network": {"manual_ip": null, "port": 9080, "auto_detect_ip": true}, "request": {"timeout_seconds": 180, "max_concurrent_requests": 20, "backpressure_queue_size": 5}, "monitoring": {"error_rate_threshold": 0.1, "response_time_threshold": 30, "active_requests_threshold": 50, "cleanup_interval": 300}, "tunnel": {"enabled": true, "type": "duckdns", "auth_token": "", "subdomain": "", "region": "eu", "custom_domain": "", "frp_server": "", "frp_port": 7000, "frp_token": "", "cloudflare_tunnel_token": "", "duckdns_domain": "ll555.duckdns.org", "duckdns_token": "2ac70818-cacc-48a1-9dc1-7360875ccc72", "available_types": [{"value": "duckdns", "label": "DuckDNS (免费动态域名)", "free": true}, {"value": "serveo", "label": "Serveo (完全免费SSH隧道)", "free": true}, {"value": "localhost_run", "label": "localhost.run (免费SSH隧道)", "free": true}, {"value": "localtunnel", "label": "LocalTunnel (需要npm)", "free": true}, {"value": "ngrok", "label": "<PERSON><PERSON> (需要注册)", "free": false}, {"value": "ngrok_open", "label": "Ngrok免费版 (需要免费注册)", "free": true}, {"value": "cloudflare", "label": "Cloudflare Tunnel (需要域名)", "free": true}, {"value": "frp", "label": "FRP (需要服务器)", "free": false}]}, "quick_links": [{"name": "监控面板", "url": "/monitor", "icon": "📊"}, {"name": "健康检查", "url": "/api/health/detailed", "icon": "🏥"}, {"name": "Prometheus", "url": "/metrics", "icon": "📈"}, {"name": "API文档", "url": "/monitor#api-docs", "icon": "📚"}]}